// AI Dungeon Master Frontend Application
class DungeonMasterApp {
    constructor() {
        this.socket = null;
        this.currentScreen = 'main-menu';
        this.sessionData = null;
        this.playerData = null;
        this.characterData = null;

        this.init();
    }

    init() {
        this.connectSocket();
        this.bindEvents();
        this.showScreen('main-menu');
    }

    connectSocket() {
        this.socket = io();

        this.socket.on('connect', () => {
            console.log('Connected to server');
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.showError('Connection lost. Please refresh the page.');
        });

        this.socket.on('error', (data) => {
            this.hideLoading();
            this.showError(data.message);
        });

        this.socket.on('session_created', (data) => {
            this.hideLoading();
            this.sessionData = data;
            this.playerData = { isHost: true };
            this.showLobby(data.sessionCode);
        });

        this.socket.on('session_updated', (sessionState) => {
            this.updateSessionState(sessionState);
        });

        this.socket.on('character_generated', (data) => {
            this.hideLoading();
            this.displayGeneratedCharacter(data.character);
        });

        this.socket.on('scenario_generated', (data) => {
            this.hideLoading();
            this.displayGeneratedScenario(data.scenario);
        });

        this.socket.on('game_started', (data) => {
            this.hideLoading();
            console.log('Game started, switching to game screen');
            this.showScreen('game');
            this.updateGameState(data.sessionState);
        });
    }

    bindEvents() {
        // Main menu events
        document.getElementById('create-game-btn').addEventListener('click', () => {
            this.createGame();
        });

        document.getElementById('join-game-btn').addEventListener('click', () => {
            this.joinGame();
        });

        // Lobby events
        document.getElementById('copy-code-btn').addEventListener('click', () => {
            this.copySessionCode();
        });

        document.getElementById('generate-scenario-btn').addEventListener('click', () => {
            this.generateScenario();
        });

        document.getElementById('custom-scenario-btn').addEventListener('click', () => {
            this.showCustomScenarioInput();
        });

        document.getElementById('save-scenario-btn').addEventListener('click', () => {
            this.saveCustomScenario();
        });

        document.getElementById('create-character-btn').addEventListener('click', () => {
            this.showCharacterForm();
        });

        document.getElementById('generate-character-btn').addEventListener('click', () => {
            this.generateCharacter();
        });

        document.getElementById('save-character-btn').addEventListener('click', () => {
            this.saveCharacter();
        });

        document.getElementById('start-game-btn').addEventListener('click', () => {
            this.startGame();
        });

        document.getElementById('leave-lobby-btn').addEventListener('click', () => {
            this.leaveLobby();
        });

        // Game events
        document.getElementById('send-action-btn').addEventListener('click', () => {
            this.sendAction();
        });

        document.getElementById('action-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendAction();
            }
        });

        // Modal events
        document.getElementById('error-ok-btn').addEventListener('click', () => {
            this.hideError();
        });
    }

    showScreen(screenId) {
        // Hide all screens
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });

        // Show target screen
        document.getElementById(screenId).classList.add('active');
        this.currentScreen = screenId;
    }

    showLoading(text = 'Loading...') {
        document.getElementById('loading-text').textContent = text;
        document.getElementById('loading').classList.remove('hidden');
    }

    hideLoading() {
        document.getElementById('loading').classList.add('hidden');
    }

    showError(message) {
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-modal').classList.remove('hidden');
    }

    hideError() {
        document.getElementById('error-modal').classList.add('hidden');
    }

    createGame() {
        const hostName = document.getElementById('host-name').value.trim();
        if (!hostName) {
            this.showError('Please enter your name');
            return;
        }

        this.showLoading('Creating game...');
        this.socket.emit('create_session', { hostName });
    }

    joinGame() {
        const sessionCode = document.getElementById('session-code').value.trim().toUpperCase();
        const playerName = document.getElementById('player-name').value.trim();

        if (!sessionCode || !playerName) {
            this.showError('Please enter both game code and your name');
            return;
        }

        this.showLoading('Joining game...');
        this.socket.emit('join_session', { sessionCode, playerName });
    }

    showLobby(sessionCode) {
        document.getElementById('lobby-session-code').textContent = sessionCode;
        this.showScreen('lobby');
    }

    copySessionCode() {
        const code = document.getElementById('lobby-session-code').textContent;
        navigator.clipboard.writeText(code).then(() => {
            // Visual feedback
            const btn = document.getElementById('copy-code-btn');
            const originalText = btn.textContent;
            btn.textContent = 'Copied!';
            setTimeout(() => {
                btn.textContent = originalText;
            }, 2000);
        });
    }

    generateScenario() {
        this.showLoading('Generating scenario...');
        this.socket.emit('generate_scenario', { preferences: {} });
    }

    showCustomScenarioInput() {
        document.getElementById('scenario-input').classList.remove('hidden');
    }

    saveCustomScenario() {
        const scenarioText = document.getElementById('custom-scenario-text').value.trim();
        if (!scenarioText) {
            this.showError('Please enter a scenario description');
            return;
        }

        this.displayCustomScenario(scenarioText);
        document.getElementById('scenario-input').classList.add('hidden');
    }

    displayGeneratedScenario(scenario) {
        const display = document.getElementById('scenario-display');
        display.innerHTML = `
            <h4>${scenario.title}</h4>
            <p><strong>Summary:</strong> ${scenario.summary}</p>
            <p><strong>Setting:</strong> ${scenario.setting}</p>
            <p><strong>Hook:</strong> ${scenario.hook}</p>
        `;
        display.classList.remove('hidden');
    }

    displayCustomScenario(scenarioText) {
        const display = document.getElementById('scenario-display');
        display.innerHTML = `
            <h4>Custom Scenario</h4>
            <p>${scenarioText}</p>
        `;
        display.classList.remove('hidden');
    }

    showCharacterForm() {
        document.getElementById('character-form').classList.remove('hidden');
    }

    generateCharacter() {
        this.showLoading('Generating character...');
        this.socket.emit('generate_character', { preferences: {} });
    }

    saveCharacter() {
        const name = document.getElementById('char-name').value.trim();
        const race = document.getElementById('char-race').value;
        const characterClass = document.getElementById('char-class').value;
        const background = document.getElementById('char-background').value.trim();

        if (!name || !race || !characterClass) {
            this.showError('Please fill in character name, race, and class');
            return;
        }

        const character = {
            name,
            race,
            class: characterClass,
            background: background || 'Folk Hero'
        };

        this.socket.emit('create_character', {
            sessionCode: this.sessionData.sessionCode,
            character
        });

        this.characterData = character;
        this.displayCharacter(character);
        document.getElementById('character-form').classList.add('hidden');
    }

    displayGeneratedCharacter(character) {
        this.characterData = character;
        this.displayCharacter(character);

        // Auto-save generated character
        this.socket.emit('create_character', {
            sessionCode: this.sessionData.sessionCode,
            character
        });
    }

    displayCharacter(character) {
        const display = document.getElementById('character-display');
        display.innerHTML = `
            <h4>${character.name}</h4>
            <p><strong>Race:</strong> ${character.race}</p>
            <p><strong>Class:</strong> ${character.class}</p>
            <p><strong>Background:</strong> ${character.background}</p>
            ${character.backstory ? `<p><strong>Backstory:</strong> ${character.backstory}</p>` : ''}
        `;
        display.classList.remove('hidden');

        // Enable start game button if host and character created
        if (this.playerData?.isHost) {
            document.getElementById('start-game-btn').disabled = false;
        }
    }

    updateSessionState(sessionState) {
        // Update players list
        this.updatePlayersList(sessionState.players);

        // Update game state if in game
        if (this.currentScreen === 'game') {
            this.updateGameState(sessionState);
        }
    }

    updatePlayersList(players) {
        const list = document.getElementById('players-list');
        list.innerHTML = '';

        players.forEach(player => {
            const playerDiv = document.createElement('div');
            playerDiv.className = `player-item ${player.isHost ? 'player-host' : ''}`;
            playerDiv.innerHTML = `
                <span>${player.name} ${player.isHost ? '(Host)' : ''}</span>
                <span>${player.isReady ? '✓ Ready' : 'Not Ready'}</span>
            `;
            list.appendChild(playerDiv);
        });
    }

    startGame() {
        if (!this.characterData) {
            this.showError('Please create a character first');
            return;
        }

        const scenarioElement = document.getElementById('scenario-display');
        if (scenarioElement.classList.contains('hidden')) {
            this.showError('Please set up a scenario first');
            return;
        }

        const scenario = document.getElementById('custom-scenario-text').value.trim() ||
            'A classic adventure awaits...';

        this.showLoading('Starting adventure...');
        this.socket.emit('start_game', {
            sessionCode: this.sessionData.sessionCode,
            scenario
        });

        // Don't switch screens here - wait for game_started event
    }

    leaveLobby() {
        this.showScreen('main-menu');
        this.sessionData = null;
        this.playerData = null;
        this.characterData = null;
    }

    sendAction() {
        const input = document.getElementById('action-input');
        const action = input.value.trim();

        if (!action) return;

        this.socket.emit('player_action', {
            sessionCode: this.sessionData.sessionCode,
            action
        });

        input.value = '';
        input.disabled = true;
        document.getElementById('send-action-btn').disabled = true;
    }

    updateGameState(sessionState) {
        // Update status
        document.getElementById('game-status').textContent = this.getStatusText(sessionState.currentStatus);

        // Update chat
        this.updateChatMessages(sessionState.chatHistory);

        // Enable/disable input based on game state
        const canAct = sessionState.currentStatus === 'AWAITING_ACTIONS';
        document.getElementById('action-input').disabled = !canAct;
        document.getElementById('send-action-btn').disabled = !canAct;
    }

    getStatusText(status) {
        const statusTexts = {
            'NARRATOR_GENERATING': 'The Dungeon Master is setting the scene...',
            'AWAITING_ACTIONS': 'What do you do?',
            'PROCESSING_ACTION': 'Processing your action...',
            'WAITING_FOR_PLAYERS': 'Waiting for other players...'
        };

        return statusTexts[status] || status;
    }

    updateChatMessages(chatHistory) {
        const container = document.getElementById('chat-messages');
        container.innerHTML = '';

        chatHistory.forEach(message => {
            const messageDiv = this.createChatMessage(message);
            container.appendChild(messageDiv);
        });

        container.scrollTop = container.scrollHeight;
    }

    createChatMessage(message) {
        const div = document.createElement('div');
        div.className = `message message-${message.type}`;

        let content = '';

        switch (message.type) {
            case 'narrator':
                content = `<div class="message-content">${message.content}</div>`;
                break;
            case 'player_action':
                content = `
                    <div class="message-header">${message.characterName}</div>
                    <div class="message-content">${message.content}</div>
                `;
                break;
            case 'action_result':
                content = `<div class="message-content">${message.content}</div>`;
                break;
            case 'skill_check':
                const successClass = message.success ? 'success' : 'failure';
                content = `
                    <div class="skill-check ${successClass}">
                        <strong>${message.characterName}</strong> - ${message.skill} Check<br>
                        Roll: ${message.roll.breakdown} vs DC ${message.dc}<br>
                        Result: <strong>${message.success ? 'Success' : 'Failure'}</strong>
                    </div>
                `;
                break;
            case 'npc_dialogue':
                content = `
                    <div class="message-header">${message.npcName}</div>
                    <div class="message-content">${message.content}</div>
                `;
                break;
            default:
                content = `<div class="message-content">${message.content}</div>`;
        }

        div.innerHTML = content;
        return div;
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new DungeonMasterApp();
});
