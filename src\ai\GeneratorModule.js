import BaseAIModule from './BaseAIModule.js';

/**
 * AI module for generating characters, scenarios, and other game content
 */
export default class GeneratorModule extends BaseAIModule {
  constructor() {
    super('structured'); // Use structured model for consistent generation
  }

  /**
   * Generate a character based on preferences
   */
  async generate<PERSON>haracter(preferences = {}) {
    const systemPrompt = `Generate a D&D 5e character based on the given preferences. Create a complete, balanced character suitable for level 1 play.

Respond with JSON containing:
- "name": Character name
- "race": D&D race
- "class": D&D class
- "background": Character background
- "stats": Object with ability scores (str, dex, con, int, wis, cha)
- "hitPoints": Starting hit points
- "maxHitPoints": Maximum hit points
- "armorClass": Armor class
- "equipment": Array of starting equipment
- "personality": Brief personality description
- "backstory": Short backstory (2-3 sentences)
- "goals": Character goals and motivations

Use standard D&D 5e rules for character creation. Ensure stats are balanced and appropriate.`;

    const preferencesText = Object.keys(preferences).length > 0 
      ? `Preferences: ${JSON.stringify(preferences, null, 2)}`
      : 'No specific preferences - create a well-rounded character';

    const userPrompt = `${preferencesText}

Generate a complete D&D character.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 800,
        temperature: 0.7
      });

      const character = this.parseJSONResponse(response.content);
      this.validateResponse(character, ['name', 'race', 'class', 'stats']);

      // Ensure stats are properly formatted
      if (typeof character.stats !== 'object') {
        character.stats = this.generateDefaultStats();
      }

      // Set defaults for missing fields
      character.level = character.level || 1;
      character.experience = character.experience || 0;
      character.hitPoints = character.hitPoints || character.maxHitPoints || 10;
      character.maxHitPoints = character.maxHitPoints || 10;
      character.armorClass = character.armorClass || 10;
      character.equipment = character.equipment || [];
      character.spells = character.spells || [];

      return {
        ...character,
        id: `char_${Date.now()}`,
        createdAt: new Date(),
        type: 'generated_character',
        processingTime: response.duration
      };
    } catch (error) {
      console.error('Character generation error:', error);
      
      // Return fallback character
      return this.createFallbackCharacter(preferences);
    }
  }

  /**
   * Generate a scenario/adventure hook
   */
  async generateScenario(preferences = {}) {
    const systemPrompt = `Create an engaging D&D adventure scenario suitable for a small party of level 1 characters.

Respond with JSON containing:
- "title": Adventure title
- "summary": Brief summary (1-2 sentences)
- "setting": Where the adventure takes place
- "hook": How the characters get involved
- "mainObjective": Primary goal of the adventure
- "challenges": Array of potential challenges/obstacles
- "rewards": Potential rewards for completion
- "estimatedDuration": Estimated play time
- "themes": Adventure themes (mystery, combat, exploration, etc.)
- "npcs": Key NPCs involved
- "locations": Important locations

Create something engaging but not overwhelming for new players.`;

    const preferencesText = Object.keys(preferences).length > 0 
      ? `Preferences: ${JSON.stringify(preferences, null, 2)}`
      : 'No specific preferences - create a balanced adventure';

    const userPrompt = `${preferencesText}

Generate a complete adventure scenario.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 1000,
        temperature: 0.8
      });

      const scenario = this.parseJSONResponse(response.content);
      this.validateResponse(scenario, ['title', 'summary', 'hook', 'mainObjective']);

      return {
        ...scenario,
        id: `scenario_${Date.now()}`,
        createdAt: new Date(),
        type: 'generated_scenario',
        processingTime: response.duration
      };
    } catch (error) {
      console.error('Scenario generation error:', error);
      
      // Return fallback scenario
      return this.createFallbackScenario();
    }
  }

  /**
   * Generate random encounters
   */
  async generateEncounter(environment, difficulty = 'easy', partyLevel = 1) {
    const systemPrompt = `Generate a D&D encounter appropriate for the given parameters.

Respond with JSON containing:
- "type": "combat", "social", "exploration", or "puzzle"
- "description": What the party encounters
- "enemies": Array of enemies (if combat)
- "npcs": NPCs involved (if social)
- "challenge": The main challenge to overcome
- "solutions": Possible ways to resolve the encounter
- "rewards": Potential rewards
- "difficulty": Encounter difficulty rating

Environment: ${environment}
Difficulty: ${difficulty}
Party Level: ${partyLevel}`;

    const userPrompt = `Generate an encounter for this environment and difficulty level.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 600,
        temperature: 0.7
      });

      const encounter = this.parseJSONResponse(response.content);
      this.validateResponse(encounter, ['type', 'description', 'challenge']);

      return {
        ...encounter,
        id: `encounter_${Date.now()}`,
        environment,
        partyLevel,
        createdAt: new Date(),
        processingTime: response.duration
      };
    } catch (error) {
      console.error('Encounter generation error:', error);
      
      return {
        id: `encounter_${Date.now()}`,
        type: 'exploration',
        description: `You encounter something interesting in the ${environment}.`,
        challenge: 'Investigate the area',
        solutions: ['Careful observation', 'Direct approach'],
        rewards: ['Experience', 'Information'],
        difficulty: difficulty,
        error: error.message
      };
    }
  }

  /**
   * Generate loot and treasure
   */
  async generateLoot(context, value = 'low') {
    const systemPrompt = `Generate appropriate loot/treasure for a D&D encounter.

Respond with JSON containing:
- "items": Array of items found
- "gold": Amount of gold pieces
- "description": How the treasure is found/presented
- "totalValue": Estimated total value in gold pieces

Value level: ${value}
Context: ${context}`;

    const userPrompt = `Generate loot appropriate for this context and value level.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 400,
        temperature: 0.6
      });

      return this.parseJSONResponse(response.content);
    } catch (error) {
      console.error('Loot generation error:', error);
      
      return {
        items: ['A small trinket'],
        gold: Math.floor(Math.random() * 20) + 5,
        description: 'You find some modest treasure.',
        totalValue: 25,
        error: error.message
      };
    }
  }

  /**
   * Create fallback character
   */
  createFallbackCharacter(preferences) {
    const races = ['Human', 'Elf', 'Dwarf', 'Halfling'];
    const classes = ['Fighter', 'Rogue', 'Wizard', 'Cleric'];
    
    const race = preferences.race || races[Math.floor(Math.random() * races.length)];
    const characterClass = preferences.class || classes[Math.floor(Math.random() * classes.length)];
    
    return {
      id: `char_${Date.now()}`,
      name: preferences.name || 'Generated Character',
      race,
      class: characterClass,
      background: 'Folk Hero',
      stats: this.generateDefaultStats(),
      hitPoints: 10,
      maxHitPoints: 10,
      armorClass: 12,
      level: 1,
      experience: 0,
      equipment: ['Backpack', 'Bedroll', 'Rations'],
      spells: [],
      personality: 'Brave and determined',
      backstory: 'A character with a mysterious past and bright future.',
      goals: 'To prove themselves as a hero',
      type: 'generated_character',
      error: 'Generated using fallback method'
    };
  }

  /**
   * Create fallback scenario
   */
  createFallbackScenario() {
    return {
      id: `scenario_${Date.now()}`,
      title: 'The Missing Merchant',
      summary: 'A local merchant has gone missing, and the party is asked to investigate.',
      setting: 'A small village and surrounding countryside',
      hook: 'The village elder approaches the party for help',
      mainObjective: 'Find the missing merchant and discover what happened',
      challenges: ['Tracking clues', 'Dealing with bandits', 'Navigating wilderness'],
      rewards: ['Gold reward', 'Merchant\'s gratitude', 'Local reputation'],
      estimatedDuration: '2-3 hours',
      themes: ['Mystery', 'Investigation', 'Light combat'],
      npcs: ['Village Elder', 'Missing Merchant', 'Bandit Leader'],
      locations: ['Village Square', 'Forest Path', 'Bandit Camp'],
      type: 'generated_scenario',
      error: 'Generated using fallback method'
    };
  }

  /**
   * Generate default character stats
   */
  generateDefaultStats() {
    return {
      strength: 13,
      dexterity: 14,
      constitution: 12,
      intelligence: 11,
      wisdom: 13,
      charisma: 10
    };
  }
}
