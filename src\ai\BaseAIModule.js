import OpenAI from 'openai';

/**
 * Base class for all AI modules
 */
export default class BaseAIModule {
  constructor(modelType = 'structured') {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });

    // Choose model based on type
    this.model = modelType === 'creative'
      ? (process.env.AI_MODEL_CREATIVE || 'gpt-4o')
      : (process.env.AI_MODEL_STRUCTURED || 'gpt-4o-mini');

    this.maxTokens = 1000;
    this.temperature = modelType === 'creative' ? 0.8 : 0.3;
  }

  /**
   * Make a request to OpenAI API
   */
  async makeRequest(messages, options = {}) {
    try {
      const startTime = Date.now();

      // Extract known options and avoid spreading unknown ones
      const {
        maxTokens,
        temperature,
        top_p,
        frequency_penalty,
        presence_penalty,
        ...otherOptions
      } = options;

      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: messages,
        max_tokens: maxTokens || this.maxTokens,
        temperature: temperature || this.temperature,
        top_p: top_p || 1,
        frequency_penalty: frequency_penalty || 0,
        presence_penalty: presence_penalty || 0
      });

      const duration = Date.now() - startTime;
      const content = response.choices[0]?.message?.content || '';

      return {
        content,
        usage: response.usage,
        duration,
        model: this.model
      };
    } catch (error) {
      console.error(`AI Module Error (${this.constructor.name}):`, error);
      throw new Error(`AI request failed: ${error.message}`);
    }
  }

  /**
   * Create system message
   */
  createSystemMessage(content) {
    return { role: 'system', content };
  }

  /**
   * Create user message
   */
  createUserMessage(content) {
    return { role: 'user', content };
  }

  /**
   * Create assistant message
   */
  createAssistantMessage(content) {
    return { role: 'assistant', content };
  }

  /**
   * Parse JSON response safely
   */
  parseJSONResponse(content) {
    try {
      // Try to extract JSON from markdown code blocks
      const jsonMatch = content.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[1]);
      }

      // Try to parse the content directly
      return JSON.parse(content);
    } catch (error) {
      console.error('Failed to parse JSON response:', content);
      throw new Error('Invalid JSON response from AI');
    }
  }

  /**
   * Validate required fields in response
   */
  validateResponse(response, requiredFields) {
    for (const field of requiredFields) {
      if (!(field in response)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    return true;
  }

  /**
   * Create context string from game state
   */
  createGameContext(session) {
    const context = {
      scenario: session.scenario,
      currentScene: session.gameContext.currentScene,
      players: Array.from(session.characters.values()).map(char => ({
        name: char.name,
        class: char.class,
        race: char.race,
        level: char.level,
        hitPoints: char.hitPoints,
        maxHitPoints: char.maxHitPoints
      })),
      worldState: session.gameContext.worldState,
      recentHistory: session.chatHistory.slice(-10) // Last 10 messages
    };

    return JSON.stringify(context, null, 2);
  }

  /**
   * Truncate text to fit within token limits
   */
  truncateText(text, maxLength = 2000) {
    // Convert to string if not already
    const textStr = String(text || '');
    if (textStr.length <= maxLength) {
      return textStr;
    }
    return textStr.substring(0, maxLength - 3) + '...';
  }
}
