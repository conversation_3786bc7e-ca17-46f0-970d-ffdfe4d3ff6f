import { v4 as uuidv4 } from 'uuid';

/**
 * Represents a game session with players, characters, and game state
 */
export default class GameSession {
  constructor(hostId, hostName) {
    this.id = uuidv4();
    this.code = this.generateSessionCode();
    this.hostId = hostId;
    this.hostName = hostName;
    this.players = new Map(); // socketId -> player object
    this.characters = new Map(); // playerId -> character object
    this.chatHistory = [];
    this.gameState = 'LOBBY'; // LOBBY, PLAYING, PAUSED, ENDED
    this.currentStatus = 'WAITING_FOR_PLAYERS';
    this.activePlayerId = null;
    this.scenario = null;
    this.createdAt = new Date();
    this.lastActivity = new Date();
    this.gameContext = {
      currentScene: '',
      worldState: {},
      npcs: [],
      inventory: {},
      questLog: []
    };
  }

  /**
   * Generate a human-readable session code
   */
  generateSessionCode() {
    const adjectives = ['BRAVE', 'WISE', 'SWIFT', 'BOLD', 'KEEN', 'WILD', 'CALM', 'DARK', 'FIRE', 'IRON'];
    const nouns = ['DRAGON', 'SWORD', 'SHIELD', 'TOWER', 'QUEST', 'MAGIC', 'STONE', 'CROWN', 'STAR', 'MOON'];

    const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    const number = Math.floor(Math.random() * 900) + 100; // 100-999

    return `${adjective}-${noun}-${number}`;
  }

  /**
   * Add a player to the session
   */
  addPlayer(socketId, playerName) {
    if (this.players.size >= 6) {
      throw new Error('Session is full');
    }

    if (this.gameState !== 'LOBBY') {
      throw new Error('Cannot join game in progress');
    }

    const player = {
      id: socketId,
      name: playerName,
      isHost: socketId === this.hostId,
      joinedAt: new Date(),
      isReady: false
    };

    this.players.set(socketId, player);
    this.updateActivity();

    return player;
  }

  /**
   * Remove a player from the session
   */
  removePlayer(socketId) {
    this.players.delete(socketId);
    this.characters.delete(socketId);
    this.updateActivity();

    // If host left, assign new host
    if (socketId === this.hostId && this.players.size > 0) {
      const newHost = this.players.values().next().value;
      this.hostId = newHost.id;
      newHost.isHost = true;
    }
  }

  /**
   * Create a character for a player
   */
  createCharacter(playerId, characterData) {
    if (!this.players.has(playerId)) {
      throw new Error('Player not found in session');
    }

    const character = {
      id: uuidv4(),
      playerId: playerId,
      name: characterData.name,
      class: characterData.class,
      race: characterData.race,
      background: characterData.background || '',
      stats: characterData.stats || this.generateDefaultStats(),
      hitPoints: characterData.hitPoints || 10,
      maxHitPoints: characterData.maxHitPoints || 10,
      armorClass: characterData.armorClass || 10,
      level: characterData.level || 1,
      experience: characterData.experience || 0,
      equipment: characterData.equipment || [],
      spells: characterData.spells || [],
      createdAt: new Date()
    };

    this.characters.set(playerId, character);
    this.updateActivity();

    return character;
  }

  /**
   * Generate default character stats
   */
  generateDefaultStats() {
    return {
      strength: 10,
      dexterity: 10,
      constitution: 10,
      intelligence: 10,
      wisdom: 10,
      charisma: 10
    };
  }

  /**
   * Add a message to chat history
   */
  addChatMessage(message) {
    const chatMessage = {
      id: uuidv4(),
      timestamp: new Date(),
      ...message
    };

    this.chatHistory.push(chatMessage);
    this.updateActivity();

    return chatMessage;
  }

  /**
   * Update game status
   */
  updateStatus(status, activePlayerId = null) {
    this.currentStatus = status;
    this.activePlayerId = activePlayerId;
    this.updateActivity();
  }

  /**
   * Start the game
   */
  startGame(scenario, language = 'English') {
    if (this.gameState !== 'LOBBY') {
      throw new Error('Game already started');
    }

    if (this.characters.size === 0) {
      throw new Error('No characters created');
    }

    this.scenario = scenario;
    this.language = language;
    this.gameState = 'PLAYING';
    this.currentStatus = 'NARRATOR_GENERATING';
    this.updateActivity();
  }

  /**
   * Update last activity timestamp
   */
  updateActivity() {
    this.lastActivity = new Date();
  }

  /**
   * Check if session is expired
   */
  isExpired() {
    const timeoutMinutes = parseInt(process.env.SESSION_TIMEOUT_MINUTES) || 120;
    const timeoutMs = timeoutMinutes * 60 * 1000;
    return (Date.now() - this.lastActivity.getTime()) > timeoutMs;
  }

  /**
   * Get current session state for clients
   */
  getState() {
    return {
      id: this.id,
      code: this.code,
      hostId: this.hostId,
      hostName: this.hostName,
      players: Array.from(this.players.values()),
      characters: Array.from(this.characters.values()),
      chatHistory: this.chatHistory,
      gameState: this.gameState,
      currentStatus: this.currentStatus,
      activePlayerId: this.activePlayerId,
      scenario: this.scenario,
      gameContext: this.gameContext,
      createdAt: this.createdAt,
      lastActivity: this.lastActivity
    };
  }

  /**
   * Get player by socket ID
   */
  getPlayer(socketId) {
    return this.players.get(socketId);
  }

  /**
   * Get character by player ID
   */
  getCharacter(playerId) {
    return this.characters.get(playerId);
  }

  /**
   * Check if all players are ready
   */
  areAllPlayersReady() {
    return Array.from(this.players.values()).every(player => player.isReady);
  }

  /**
   * Set player ready status
   */
  setPlayerReady(playerId, isReady = true) {
    const player = this.players.get(playerId);
    if (player) {
      player.isReady = isReady;
      this.updateActivity();
    }
  }

  /**
   * Update game context
   */
  updateGameContext(updates) {
    this.gameContext = { ...this.gameContext, ...updates };
    this.updateActivity();
  }
}
