import fs from 'fs-extra';
import path from 'path';

/**
 * Logger utility for structured logging in JSONL format
 */
export default class Logger {
  constructor() {
    this.logDir = process.env.LOG_DIR || './logs';
    this.logLevel = process.env.LOG_LEVEL || 'info';
    this.ensureLogDirectory();
  }

  /**
   * Ensure log directory exists
   */
  async ensureLogDirectory() {
    try {
      await fs.ensureDir(this.logDir);
    } catch (error) {
      console.error('Failed to create log directory:', error);
    }
  }

  /**
   * Get log file path for current date
   */
  getLogFilePath() {
    const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    return path.join(this.logDir, `neurodnd-${date}.jsonl`);
  }

  /**
   * Write log entry to file
   */
  async writeLog(level, message, data = {}) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      level: level.toUpperCase(),
      message,
      ...data
    };

    const logLine = JSON.stringify(logEntry) + '\n';

    try {
      await fs.appendFile(this.getLogFilePath(), logLine);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }

    // Also log to console in development
    if (process.env.NODE_ENV === 'development') {
      const consoleMessage = `[${logEntry.timestamp}] ${level.toUpperCase()}: ${message}`;
      if (Object.keys(data).length > 0) {
        console.log(consoleMessage, data);
      } else {
        console.log(consoleMessage);
      }
    }
  }

  /**
   * Log info level message
   */
  info(message, data = {}) {
    this.writeLog('info', message, data);
  }

  /**
   * Log warning level message
   */
  warn(message, data = {}) {
    this.writeLog('warn', message, data);
  }

  /**
   * Log error level message
   */
  error(message, data = {}) {
    this.writeLog('error', message, data);
  }

  /**
   * Log debug level message
   */
  debug(message, data = {}) {
    if (this.logLevel === 'debug') {
      this.writeLog('debug', message, data);
    }
  }

  /**
   * Log session event
   */
  logSessionEvent(sessionId, event, payload = {}) {
    this.info('Session event', {
      sessionId,
      event,
      payload
    });
  }

  /**
   * Log LLM request
   */
  logLLMRequest(sessionId, module, prompt, model) {
    this.info('LLM request', {
      sessionId,
      module,
      model,
      promptLength: prompt.length,
      event: 'LLM_REQUEST'
    });
  }

  /**
   * Log LLM response
   */
  logLLMResponse(sessionId, module, response, model, duration) {
    this.info('LLM response', {
      sessionId,
      module,
      model,
      responseLength: response.length,
      duration,
      event: 'LLM_RESPONSE'
    });
  }

  /**
   * Log player action
   */
  logPlayerAction(sessionId, playerId, playerName, action) {
    this.info('Player action', {
      sessionId,
      playerId,
      playerName,
      action,
      event: 'PLAYER_ACTION'
    });
  }

  /**
   * Log state update
   */
  logStateUpdate(sessionId, oldStatus, newStatus, activePlayerId = null) {
    this.info('State update', {
      sessionId,
      oldStatus,
      newStatus,
      activePlayerId,
      event: 'STATE_UPDATE'
    });
  }

  /**
   * Log game start
   */
  logGameStart(sessionId, scenario, playerCount) {
    this.info('Game started', {
      sessionId,
      scenario: scenario.substring(0, 100) + '...', // Truncate for logging
      playerCount,
      event: 'GAME_START'
    });
  }

  /**
   * Log error with context
   */
  logError(error, context = {}) {
    this.error(error.message, {
      error: error.name,
      stack: error.stack,
      ...context,
      event: 'ERROR'
    });
  }
}
