import ClassifierModule from '../ai/ClassifierModule.js';
import RulesModule from '../ai/RulesModule.js';
import NarrativeModule from '../ai/NarrativeModule.js';
import NPCModule from '../ai/NPCModule.js';
import GeneratorModule from '../ai/GeneratorModule.js';
import Logger from '../utils/Logger.js';

/**
 * Main game controller that coordinates AI modules and manages game flow
 */
export default class GameController {
  constructor(sessionManager) {
    this.sessionManager = sessionManager;
    this.logger = new Logger();

    // Initialize AI modules
    this.classifier = new ClassifierModule();
    this.rules = new RulesModule();
    this.narrative = new NarrativeModule();
    this.npc = new NPCModule();
    this.generator = new GeneratorModule();

    // Track active processing
    this.activeProcessing = new Map(); // sessionId -> processing state
  }

  /**
   * Start a new game session
   */
  async startGame(sessionId, scenario, language = 'English') {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    try {
      this.logger.logGameStart(sessionId, scenario, session.players.size);

      // Update session state
      session.startGame(scenario, language);
      session.updateStatus('NARRATOR_GENERATING');

      // Generate opening scene
      const characters = Array.from(session.characters.values());
      const openingScene = await this.narrative.generateOpeningScene(scenario, characters, language);

      // Add opening message to chat
      session.addChatMessage({
        type: 'narrator',
        content: openingScene.description,
        timestamp: new Date()
      });

      // Update status to await player actions
      session.updateStatus('AWAITING_ACTIONS');

      this.logger.logStateUpdate(sessionId, 'NARRATOR_GENERATING', 'AWAITING_ACTIONS');

      return session.getState();
    } catch (error) {
      this.logger.logError(error, { sessionId, event: 'GAME_START_ERROR' });
      throw error;
    }
  }

  /**
   * Handle a player action
   */
  async handlePlayerAction(sessionCode, playerId, action) {
    const session = this.sessionManager.getSessionByCode(sessionCode);
    if (!session) {
      throw new Error('Session not found');
    }

    const player = session.getPlayer(playerId);
    const character = session.getCharacter(playerId);

    if (!player || !character) {
      throw new Error('Player or character not found');
    }

    try {
      this.logger.logPlayerAction(session.id, playerId, player.name, action);

      // Add player action to chat
      session.addChatMessage({
        type: 'player_action',
        playerId: playerId,
        playerName: player.name,
        characterName: character.name,
        content: action,
        timestamp: new Date()
      });

      // Update session status
      session.updateStatus('PROCESSING_ACTION', playerId);

      // Process the action through AI pipeline
      const result = await this.processPlayerAction(session, character, action);

      // Add result to chat
      session.addChatMessage({
        type: 'action_result',
        playerId: playerId,
        characterName: character.name,
        content: result.description,
        success: result.success,
        timestamp: new Date()
      });

      // Update session status back to awaiting actions
      session.updateStatus('AWAITING_ACTIONS');

      this.logger.logStateUpdate(session.id, 'PROCESSING_ACTION', 'AWAITING_ACTIONS');

      return result;
    } catch (error) {
      this.logger.logError(error, {
        sessionId: session.id,
        playerId,
        action,
        event: 'PLAYER_ACTION_ERROR'
      });

      // Reset session status on error
      session.updateStatus('AWAITING_ACTIONS');
      throw error;
    }
  }

  /**
   * Process a player action through the AI pipeline
   */
  async processPlayerAction(session, character, action) {
    const gameContext = this.createGameContext(session);

    try {
      // Step 1: Classify the action
      this.logger.logLLMRequest(session.id, 'Classifier', action, 'gpt-4o-mini');
      const startTime = Date.now();

      const classification = await this.classifier.classifyAction(action, gameContext);

      this.logger.logLLMResponse(
        session.id,
        'Classifier',
        JSON.stringify(classification),
        'gpt-4o-mini',
        Date.now() - startTime
      );

      // Step 2: Determine mechanical requirements
      this.logger.logLLMRequest(session.id, 'Rules', JSON.stringify(classification), 'gpt-4o-mini');
      const rulesStartTime = Date.now();

      const mechanics = await this.rules.interpretAction(classification, character, gameContext);

      this.logger.logLLMResponse(
        session.id,
        'Rules',
        JSON.stringify(mechanics),
        'gpt-4o-mini',
        Date.now() - rulesStartTime
      );

      // Step 3: Execute mechanics (dice rolls, etc.)
      let actionResult = {
        classification,
        mechanics,
        character: character.name,
        success: true
      };

      if (mechanics.mechanicType === 'skill_check') {
        const skillCheck = this.rules.performSkillCheck(
          character,
          mechanics.skill,
          mechanics.dc,
          mechanics.advantage,
          mechanics.modifiers || []
        );

        actionResult = { ...actionResult, ...skillCheck };

        // Add skill check result to chat
        session.addChatMessage({
          type: 'skill_check',
          characterName: character.name,
          skill: mechanics.skill,
          roll: skillCheck.roll,
          dc: mechanics.dc,
          success: skillCheck.success,
          timestamp: new Date()
        });
      }

      // Step 4: Generate narrative description
      this.logger.logLLMRequest(session.id, 'Narrative', JSON.stringify(actionResult), 'gpt-4o');
      const narrativeStartTime = Date.now();

      const description = await this.narrative.describeActionResult(actionResult, gameContext, session.language);

      this.logger.logLLMResponse(
        session.id,
        'Narrative',
        description.description,
        'gpt-4o',
        Date.now() - narrativeStartTime
      );

      actionResult.description = description.description;

      return actionResult;
    } catch (error) {
      this.logger.logError(error, {
        sessionId: session.id,
        character: character.name,
        action,
        event: 'ACTION_PROCESSING_ERROR'
      });

      // Return fallback result
      return {
        classification: { category: 'QUESTION', intent: action },
        character: character.name,
        success: false,
        description: `${character.name} attempts to ${action}, but the outcome is unclear. The Dungeon Master will need to determine what happens next.`,
        error: error.message
      };
    }
  }

  /**
   * Generate a character using AI
   */
  async generateCharacter(preferences = {}, language = 'English') {
    try {
      this.logger.info('Generating character', { preferences, language });
      return await this.generator.generateCharacter(preferences, language);
    } catch (error) {
      this.logger.logError(error, { event: 'CHARACTER_GENERATION_ERROR', preferences, language });
      throw error;
    }
  }

  /**
   * Generate a scenario using AI
   */
  async generateScenario(preferences = {}, language = 'English') {
    try {
      this.logger.info('Generating scenario', { preferences, language });
      return await this.generator.generateScenario(preferences, language);
    } catch (error) {
      this.logger.logError(error, { event: 'SCENARIO_GENERATION_ERROR', preferences, language });
      throw error;
    }
  }

  /**
   * Handle NPC dialogue
   */
  async handleNPCDialogue(sessionId, npcId, playerMessage) {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    try {
      // Find NPC in game context
      const npc = session.gameContext.npcs.find(n => n.id === npcId);
      if (!npc) {
        throw new Error('NPC not found');
      }

      const gameContext = this.createGameContext(session);
      const response = await this.npc.generateNPCResponse(npc, playerMessage, gameContext);

      // Add NPC response to chat
      session.addChatMessage({
        type: 'npc_dialogue',
        npcId: npcId,
        npcName: npc.name,
        content: response.response,
        timestamp: new Date()
      });

      this.logger.info('NPC dialogue generated', {
        sessionId,
        npcId,
        npcName: npc.name,
        playerMessage: playerMessage.substring(0, 100)
      });

      return response;
    } catch (error) {
      this.logger.logError(error, { sessionId, npcId, event: 'NPC_DIALOGUE_ERROR' });
      throw error;
    }
  }

  /**
   * Create game context for AI modules
   */
  createGameContext(session) {
    return {
      scenario: session.scenario,
      currentScene: session.gameContext.currentScene,
      players: Array.from(session.characters.values()).map(char => ({
        name: char.name,
        class: char.class,
        race: char.race,
        level: char.level,
        hitPoints: char.hitPoints,
        maxHitPoints: char.maxHitPoints
      })),
      worldState: session.gameContext.worldState,
      npcs: session.gameContext.npcs,
      recentHistory: session.chatHistory.slice(-10),
      gameState: session.gameState,
      currentStatus: session.currentStatus
    };
  }

  /**
   * Get session statistics for monitoring
   */
  getSessionStats(sessionId) {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) {
      return null;
    }

    return {
      sessionId,
      playerCount: session.players.size,
      characterCount: session.characters.size,
      messageCount: session.chatHistory.length,
      gameState: session.gameState,
      currentStatus: session.currentStatus,
      uptime: Date.now() - session.createdAt.getTime(),
      lastActivity: session.lastActivity
    };
  }
}
