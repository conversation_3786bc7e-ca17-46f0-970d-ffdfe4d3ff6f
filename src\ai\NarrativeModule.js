import BaseAIModule from './BaseAIModule.js';

/**
 * AI module for generating narrative descriptions and story content
 */
export default class NarrativeModule extends BaseAIModule {
  constructor() {
    super('creative'); // Use creative model for narrative generation
  }

  /**
   * Generate opening scene description
   */
  async generateOpeningScene(scenario, characters, language = 'English') {
    const systemPrompt = `You are a skilled Dungeon Master creating an immersive opening scene for a D&D adventure.

IMPORTANT: Generate ALL narrative content in ${language}. Write the entire scene description in ${language}.

GUIDELINES:
- Create vivid, engaging descriptions that set the mood
- Include sensory details (sight, sound, smell, touch)
- Establish the setting and atmosphere
- Introduce the scenario naturally
- Leave room for player agency and choice
- Keep descriptions concise but evocative (2-3 paragraphs max)
- End with a clear situation that invites player action

Characters in the party:
${characters.map(char => `- ${char.name} (${char.race} ${char.class})`).join('\n')}`;

    const userPrompt = `Scenario: ${scenario}

Create an opening scene that draws the players into this adventure. Set the stage for their first actions.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 600,
        temperature: 0.8
      });

      return {
        description: response.content.trim(),
        type: 'opening_scene',
        processingTime: response.duration
      };
    } catch (error) {
      console.error('Opening scene generation error:', error);
      return {
        description: "You find yourselves at the beginning of a new adventure. The path ahead is uncertain, but your resolve is strong. What do you do?",
        type: 'opening_scene',
        error: error.message
      };
    }
  }

  /**
   * Describe the results of player actions
   */
  async describeActionResult(actionResult, gameContext, language = 'English') {
    const systemPrompt = `You are a Dungeon Master describing the results of a player's action in vivid, engaging detail.

IMPORTANT: Generate ALL narrative content in ${language}. Write the entire description in ${language}.

GUIDELINES:
- Be descriptive but concise
- Match the tone to the action (dramatic for combat, mysterious for investigation, etc.)
- Include consequences and new information revealed
- Describe both success and failure in interesting ways
- Maintain narrative flow and pacing
- Include sensory details when appropriate
- Set up the next moment for player choice

Current game context: ${this.truncateText(gameContext)}`;

    const resultText = this.formatActionResult(actionResult);
    const userPrompt = `Action Result to Describe:
${resultText}

Provide a vivid, engaging description of what happens.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 400,
        temperature: 0.7
      });

      return {
        description: response.content.trim(),
        type: 'action_result',
        actionType: actionResult.classification?.category,
        success: actionResult.success,
        processingTime: response.duration
      };
    } catch (error) {
      console.error('Action result description error:', error);
      return {
        description: this.generateFallbackDescription(actionResult),
        type: 'action_result',
        error: error.message
      };
    }
  }

  /**
   * Generate environmental descriptions
   */
  async describeEnvironment(location, context, focusElements = []) {
    const systemPrompt = `You are describing a location in a D&D adventure. Create an immersive description that helps players visualize the scene.

GUIDELINES:
- Use vivid sensory details
- Highlight important or interactive elements
- Create atmosphere appropriate to the setting
- Include potential points of interest
- Keep descriptions focused and not overwhelming
- Suggest possibilities without forcing actions

${focusElements.length > 0 ? `Pay special attention to: ${focusElements.join(', ')}` : ''}

Game context: ${this.truncateText(context)}`;

    const userPrompt = `Location to describe: ${location}

Create an evocative description of this location.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 500,
        temperature: 0.8
      });

      return {
        description: response.content.trim(),
        type: 'environment',
        location,
        processingTime: response.duration
      };
    } catch (error) {
      console.error('Environment description error:', error);
      return {
        description: `You find yourself in ${location}. The area is quiet, waiting for your next move.`,
        type: 'environment',
        location,
        error: error.message
      };
    }
  }

  /**
   * Generate consequences and story progression
   */
  async generateConsequences(actions, gameContext) {
    const systemPrompt = `You are a Dungeon Master determining the consequences and story progression based on recent player actions.

GUIDELINES:
- Consider both immediate and long-term consequences
- Maintain story coherence and logic
- Create interesting complications or developments
- Reward clever thinking and good roleplay
- Keep the story moving forward
- Balance success and challenge

Recent actions and context: ${this.truncateText(gameContext)}`;

    const actionsText = actions.map(action =>
      `${action.character}: ${action.description} (${action.success ? 'Success' : 'Failure'})`
    ).join('\n');

    const userPrompt = `Recent Actions:
${actionsText}

Describe the consequences and how the story progresses from here.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 600,
        temperature: 0.7
      });

      return {
        description: response.content.trim(),
        type: 'consequences',
        processingTime: response.duration
      };
    } catch (error) {
      console.error('Consequences generation error:', error);
      return {
        description: "The situation continues to unfold. What do you do next?",
        type: 'consequences',
        error: error.message
      };
    }
  }

  /**
   * Format action result for description
   */
  formatActionResult(actionResult) {
    let result = `Character: ${actionResult.character}\n`;
    result += `Action: ${actionResult.classification?.originalAction}\n`;

    if (actionResult.roll) {
      result += `Roll: ${actionResult.roll.breakdown} vs DC ${actionResult.dc}\n`;
      result += `Result: ${actionResult.success ? 'Success' : 'Failure'}\n`;

      if (actionResult.criticalSuccess) result += `Critical Success!\n`;
      if (actionResult.criticalFailure) result += `Critical Failure!\n`;
    }

    if (actionResult.damage) {
      result += `Damage: ${actionResult.damage}\n`;
    }

    return result;
  }

  /**
   * Generate fallback description for errors
   */
  generateFallbackDescription(actionResult) {
    if (actionResult.success) {
      return `${actionResult.character}'s action succeeds. The situation develops as intended.`;
    } else {
      return `${actionResult.character}'s action doesn't go as planned. The challenge remains.`;
    }
  }

  /**
   * Generate transition text between scenes
   */
  async generateTransition(fromScene, toScene, method = 'travel') {
    const systemPrompt = `Create a brief transition description for moving between scenes in a D&D adventure.

Keep it concise (1-2 sentences) but evocative. Match the tone to the transition method.`;

    const userPrompt = `Transition from: ${fromScene}
Transition to: ${toScene}
Method: ${method}

Create a smooth transition description.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 150,
        temperature: 0.6
      });

      return {
        description: response.content.trim(),
        type: 'transition',
        fromScene,
        toScene,
        method
      };
    } catch (error) {
      return {
        description: `You make your way from ${fromScene} to ${toScene}.`,
        type: 'transition',
        error: error.message
      };
    }
  }
}
