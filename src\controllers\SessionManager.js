import GameSession from '../models/GameSession.js';

/**
 * Manages all game sessions
 */
export default class SessionManager {
  constructor() {
    this.sessions = new Map(); // sessionId -> GameSession
    this.sessionCodes = new Map(); // sessionCode -> sessionId
    this.playerSessions = new Map(); // playerId -> sessionId

    // Start cleanup interval
    this.startCleanupInterval();
  }

  /**
   * Create a new game session
   */
  async createSession(hostName, hostId) {
    const session = new GameSession(hostId, hostName);

    // Ensure unique session code
    let attempts = 0;
    while (this.sessionCodes.has(session.code) && attempts < 10) {
      session.code = session.generateSessionCode();
      attempts++;
    }

    if (this.sessionCodes.has(session.code)) {
      throw new Error('Failed to generate unique session code');
    }

    this.sessions.set(session.id, session);
    this.sessionCodes.set(session.code, session.id);

    // Add the host as the first player
    session.addPlayer(hostId, hostName);
    this.playerSessions.set(hostId, session.id);

    return session;
  }

  /**
   * Join an existing session
   */
  async joinSession(sessionCode, playerName, playerId) {
    const sessionId = this.sessionCodes.get(sessionCode);
    if (!sessionId) {
      throw new Error('Session not found');
    }

    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    if (session.isExpired()) {
      this.removeSession(sessionId);
      throw new Error('Session has expired');
    }

    // Remove player from any previous session
    this.removePlayerFromAllSessions(playerId);

    // Add player to session (only if not already in session)
    if (!session.players.has(playerId)) {
      const player = session.addPlayer(playerId, playerName);
      this.playerSessions.set(playerId, sessionId);
    }

    return session;
  }

  /**
   * Get session by ID
   */
  getSession(sessionId) {
    return this.sessions.get(sessionId);
  }

  /**
   * Get session by code
   */
  getSessionByCode(sessionCode) {
    const sessionId = this.sessionCodes.get(sessionCode);
    return sessionId ? this.sessions.get(sessionId) : null;
  }

  /**
   * Get session by player ID
   */
  getSessionByPlayer(playerId) {
    const sessionId = this.playerSessions.get(playerId);
    return sessionId ? this.sessions.get(sessionId) : null;
  }

  /**
   * Remove a session
   */
  removeSession(sessionId) {
    const session = this.sessions.get(sessionId);
    if (session) {
      // Remove all players from tracking
      for (const playerId of session.players.keys()) {
        this.playerSessions.delete(playerId);
      }

      // Remove session code mapping
      this.sessionCodes.delete(session.code);

      // Remove session
      this.sessions.delete(sessionId);
    }
  }

  /**
   * Handle player disconnect
   */
  handlePlayerDisconnect(playerId) {
    const session = this.getSessionByPlayer(playerId);
    if (session) {
      session.removePlayer(playerId);
      this.playerSessions.delete(playerId);

      // If no players left, remove session
      if (session.players.size === 0) {
        this.removeSession(session.id);
      }
    }
  }

  /**
   * Remove player from all sessions
   */
  removePlayerFromAllSessions(playerId) {
    const currentSession = this.getSessionByPlayer(playerId);
    if (currentSession) {
      currentSession.removePlayer(playerId);
      this.playerSessions.delete(playerId);

      // If no players left, remove session
      if (currentSession.players.size === 0) {
        this.removeSession(currentSession.id);
      }
    }
  }

  /**
   * Get all active sessions
   */
  getAllSessions() {
    return Array.from(this.sessions.values());
  }

  /**
   * Get session statistics
   */
  getStats() {
    const sessions = this.getAllSessions();
    const totalPlayers = sessions.reduce((sum, session) => sum + session.players.size, 0);

    return {
      totalSessions: sessions.length,
      totalPlayers: totalPlayers,
      lobbySessions: sessions.filter(s => s.gameState === 'LOBBY').length,
      activeSessions: sessions.filter(s => s.gameState === 'PLAYING').length,
      pausedSessions: sessions.filter(s => s.gameState === 'PAUSED').length,
      endedSessions: sessions.filter(s => s.gameState === 'ENDED').length
    };
  }

  /**
   * Start cleanup interval to remove expired sessions
   */
  startCleanupInterval() {
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 5 * 60 * 1000); // Check every 5 minutes
  }

  /**
   * Remove expired sessions
   */
  cleanupExpiredSessions() {
    const expiredSessions = [];

    for (const session of this.sessions.values()) {
      if (session.isExpired()) {
        expiredSessions.push(session.id);
      }
    }

    for (const sessionId of expiredSessions) {
      console.log(`Removing expired session: ${sessionId}`);
      this.removeSession(sessionId);
    }

    if (expiredSessions.length > 0) {
      console.log(`Cleaned up ${expiredSessions.length} expired sessions`);
    }
  }

  /**
   * Force cleanup all sessions (for testing/shutdown)
   */
  cleanup() {
    this.sessions.clear();
    this.sessionCodes.clear();
    this.playerSessions.clear();
  }
}
