import BaseAIModule from './BaseAIModule.js';

/**
 * AI module for interpreting D&D rules and determining game mechanics
 */
export default class RulesModule extends BaseAIModule {
  constructor() {
    super('structured'); // Use structured model for rules interpretation
  }

  /**
   * Determine the mechanical requirements for an action
   */
  async interpretAction(classification, character, gameContext) {
    const systemPrompt = `You are a D&D 5e rules expert. Given a classified player action and character information, determine the exact mechanical requirements.

RULES TO FOLLOW:
- Use standard D&D 5e rules and mechanics
- Consider character class, level, and abilities
- Determine appropriate skill checks, saving throws, or attack rolls
- Set realistic Difficulty Classes (DC) based on the situation
- Consider advantage/disadvantage circumstances
- Account for character equipment and spells

Respond with a JSON object containing:
- "mechanicType": "skill_check", "attack_roll", "saving_throw", "automatic", or "impossible"
- "skill": skill name (if skill check)
- "ability": ability score to use
- "dc": difficulty class (if applicable)
- "advantage": true/false/null
- "modifiers": array of applicable modifiers
- "requirements": any special requirements or conditions
- "consequences": potential outcomes (success/failure)
- "canAttempt": boolean - whether the action is possible

Character: ${JSON.stringify(character, null, 2)}
Game Context: ${this.truncateText(gameContext)}`;

    const userPrompt = `Classified Action:
Category: ${classification.category}
Intent: ${classification.intent}
Target: ${classification.target || 'none'}
Original Action: ${classification.originalAction}

Determine the mechanical requirements for this action.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 500,
        temperature: 0.2
      });

      const mechanics = this.parseJSONResponse(response.content);
      this.validateResponse(mechanics, ['mechanicType', 'canAttempt']);

      return {
        ...mechanics,
        classification,
        character: character.name,
        processingTime: response.duration
      };
    } catch (error) {
      console.error('Rules interpretation error:', error);

      // Return safe fallback
      return {
        mechanicType: 'skill_check',
        skill: 'Investigation',
        ability: 'Intelligence',
        dc: 12,
        advantage: null,
        modifiers: [],
        requirements: [],
        consequences: {
          success: 'Action succeeds',
          failure: 'Action fails'
        },
        canAttempt: true,
        classification,
        character: character.name,
        error: error.message
      };
    }
  }

  /**
   * Calculate dice roll results
   */
  rollDice(diceExpression, modifiers = []) {
    try {
      // Parse dice expression (e.g., "1d20", "2d6+3", "1d20+5")
      const match = diceExpression.match(/(\d+)d(\d+)(?:\+(\d+))?/);
      if (!match) {
        throw new Error('Invalid dice expression');
      }

      const numDice = parseInt(match[1]);
      const dieSize = parseInt(match[2]);
      const baseModifier = parseInt(match[3] || 0);

      const rolls = [];
      let total = baseModifier;

      for (let i = 0; i < numDice; i++) {
        const roll = Math.floor(Math.random() * dieSize) + 1;
        rolls.push(roll);
        total += roll;
      }

      // Apply additional modifiers
      const totalModifier = modifiers.reduce((sum, mod) => sum + mod.value, 0);
      total += totalModifier;

      return {
        expression: diceExpression,
        rolls,
        baseModifier,
        additionalModifiers: modifiers,
        totalModifier,
        total,
        breakdown: `${rolls.join('+')}${baseModifier > 0 ? `+${baseModifier}` : ''}${totalModifier !== 0 ? `+${totalModifier}` : ''} = ${total}`
      };
    } catch (error) {
      console.error('Dice roll error:', error);
      return {
        expression: diceExpression,
        rolls: [10], // Default middle value
        total: 10,
        error: error.message
      };
    }
  }

  /**
   * Perform a skill check
   */
  performSkillCheck(character, skill, dc, advantage = null, modifiers = []) {
    // Get ability modifier for the skill
    const skillAbilities = {
      'Acrobatics': 'dexterity',
      'Animal Handling': 'wisdom',
      'Arcana': 'intelligence',
      'Athletics': 'strength',
      'Deception': 'charisma',
      'History': 'intelligence',
      'Insight': 'wisdom',
      'Intimidation': 'charisma',
      'Investigation': 'intelligence',
      'Medicine': 'wisdom',
      'Nature': 'intelligence',
      'Perception': 'wisdom',
      'Performance': 'charisma',
      'Persuasion': 'charisma',
      'Religion': 'intelligence',
      'Sleight of Hand': 'dexterity',
      'Stealth': 'dexterity',
      'Survival': 'wisdom'
    };

    const ability = skillAbilities[skill] || 'intelligence';
    const abilityScore = character.stats[ability] || 10;
    const abilityModifier = Math.floor((abilityScore - 10) / 2);

    // Calculate proficiency bonus (simplified)
    const proficiencyBonus = Math.ceil(character.level / 4) + 1;

    // Add ability modifier and potentially proficiency bonus
    const skillModifiers = [
      { name: `${ability} modifier`, value: abilityModifier },
      { name: 'proficiency bonus', value: proficiencyBonus }
    ];

    // Add any additional modifiers
    skillModifiers.push(...modifiers);

    let rollResult;

    if (advantage === true) {
      // Roll twice, take higher
      const roll1 = this.rollDice('1d20', skillModifiers);
      const roll2 = this.rollDice('1d20', skillModifiers);
      rollResult = roll1.total >= roll2.total ? roll1 : roll2;
      rollResult.advantage = true;
      rollResult.discardedRoll = roll1.total >= roll2.total ? roll2.total : roll1.total;
    } else if (advantage === false) {
      // Roll twice, take lower
      const roll1 = this.rollDice('1d20', skillModifiers);
      const roll2 = this.rollDice('1d20', skillModifiers);
      rollResult = roll1.total <= roll2.total ? roll1 : roll2;
      rollResult.disadvantage = true;
      rollResult.discardedRoll = roll1.total <= roll2.total ? roll2.total : roll1.total;
    } else {
      // Normal roll
      rollResult = this.rollDice('1d20', skillModifiers);
    }

    const success = rollResult.total >= dc;
    const criticalSuccess = rollResult.rolls[0] === 20;
    const criticalFailure = rollResult.rolls[0] === 1;

    return {
      skill,
      ability,
      dc,
      roll: rollResult,
      success,
      criticalSuccess,
      criticalFailure,
      margin: rollResult.total - dc,
      character: character.name
    };
  }

  /**
   * Determine combat mechanics
   */
  async resolveCombatAction(action, attacker, target, gameContext) {
    const systemPrompt = `You are resolving a combat action in D&D 5e. Determine the mechanical resolution.

Consider:
- Attack rolls vs AC
- Damage calculations
- Special abilities
- Combat conditions
- Range and positioning

Respond with JSON containing:
- "attackType": "melee", "ranged", "spell"
- "toHit": attack roll modifier
- "damage": damage expression (e.g., "1d8+3")
- "damageType": type of damage
- "effects": any special effects
- "requirements": what's needed for the attack

Attacker: ${JSON.stringify(attacker, null, 2)}
Target: ${target ? JSON.stringify(target, null, 2) : 'Unknown'}
Context: ${this.truncateText(gameContext)}`;

    const userPrompt = `Combat Action: ${action}

Resolve this combat action mechanically.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 400,
        temperature: 0.3
      });

      return this.parseJSONResponse(response.content);
    } catch (error) {
      console.error('Combat resolution error:', error);

      // Return basic attack fallback
      return {
        attackType: 'melee',
        toHit: 5,
        damage: '1d6+3',
        damageType: 'slashing',
        effects: [],
        requirements: [],
        error: error.message
      };
    }
  }
}
