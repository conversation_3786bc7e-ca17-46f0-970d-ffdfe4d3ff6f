<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Dungeon Master</title>
    <link rel="stylesheet" href="styles.css">
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div id="app">
        <!-- Main Menu Screen -->
        <div id="main-menu" class="screen active">
            <div class="container">
                <header class="header">
                    <h1>🎲 AI Dungeon Master</h1>
                    <p>Experience D&D adventures powered by AI</p>
                </header>
                
                <div class="menu-options">
                    <div class="option-card">
                        <h3>Create New Game</h3>
                        <p>Start a new adventure as the host</p>
                        <input type="text" id="host-name" placeholder="Your name" maxlength="20">
                        <button id="create-game-btn" class="btn btn-primary">Create Game</button>
                    </div>
                    
                    <div class="option-card">
                        <h3>Join Game</h3>
                        <p>Join an existing adventure</p>
                        <input type="text" id="session-code" placeholder="Game code (e.g., BRAVE-DRAGON-123)" maxlength="20">
                        <input type="text" id="player-name" placeholder="Your name" maxlength="20">
                        <button id="join-game-btn" class="btn btn-secondary">Join Game</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lobby Screen -->
        <div id="lobby" class="screen">
            <div class="container">
                <header class="lobby-header">
                    <h2>Game Lobby</h2>
                    <div class="session-info">
                        <span>Game Code: <strong id="lobby-session-code"></strong></span>
                        <button id="copy-code-btn" class="btn btn-small">Copy</button>
                    </div>
                </header>

                <div class="lobby-content">
                    <div class="players-section">
                        <h3>Players</h3>
                        <div id="players-list" class="players-list"></div>
                    </div>

                    <div class="scenario-section">
                        <h3>Adventure Setup</h3>
                        <div class="scenario-options">
                            <button id="generate-scenario-btn" class="btn btn-secondary">Generate Scenario</button>
                            <button id="custom-scenario-btn" class="btn btn-secondary">Custom Scenario</button>
                        </div>
                        <div id="scenario-input" class="scenario-input hidden">
                            <textarea id="custom-scenario-text" placeholder="Describe your adventure scenario..."></textarea>
                            <button id="save-scenario-btn" class="btn btn-primary">Save Scenario</button>
                        </div>
                        <div id="scenario-display" class="scenario-display"></div>
                    </div>

                    <div class="character-section">
                        <h3>Your Character</h3>
                        <div id="character-creation" class="character-creation">
                            <div class="character-options">
                                <button id="create-character-btn" class="btn btn-secondary">Create Character</button>
                                <button id="generate-character-btn" class="btn btn-secondary">Generate Character</button>
                            </div>
                            <div id="character-form" class="character-form hidden">
                                <input type="text" id="char-name" placeholder="Character name">
                                <select id="char-race">
                                    <option value="">Select Race</option>
                                    <option value="Human">Human</option>
                                    <option value="Elf">Elf</option>
                                    <option value="Dwarf">Dwarf</option>
                                    <option value="Halfling">Halfling</option>
                                    <option value="Dragonborn">Dragonborn</option>
                                    <option value="Gnome">Gnome</option>
                                    <option value="Half-Elf">Half-Elf</option>
                                    <option value="Half-Orc">Half-Orc</option>
                                    <option value="Tiefling">Tiefling</option>
                                </select>
                                <select id="char-class">
                                    <option value="">Select Class</option>
                                    <option value="Fighter">Fighter</option>
                                    <option value="Wizard">Wizard</option>
                                    <option value="Rogue">Rogue</option>
                                    <option value="Cleric">Cleric</option>
                                    <option value="Ranger">Ranger</option>
                                    <option value="Barbarian">Barbarian</option>
                                    <option value="Bard">Bard</option>
                                    <option value="Druid">Druid</option>
                                    <option value="Monk">Monk</option>
                                    <option value="Paladin">Paladin</option>
                                    <option value="Sorcerer">Sorcerer</option>
                                    <option value="Warlock">Warlock</option>
                                </select>
                                <input type="text" id="char-background" placeholder="Background (optional)">
                                <button id="save-character-btn" class="btn btn-primary">Create Character</button>
                            </div>
                        </div>
                        <div id="character-display" class="character-display"></div>
                    </div>
                </div>

                <div class="lobby-actions">
                    <button id="start-game-btn" class="btn btn-primary" disabled>Start Adventure</button>
                    <button id="leave-lobby-btn" class="btn btn-secondary">Leave</button>
                </div>
            </div>
        </div>

        <!-- Game Screen -->
        <div id="game" class="screen">
            <div class="game-container">
                <header class="game-header">
                    <h3 id="game-title">Adventure in Progress</h3>
                    <div class="game-info">
                        <span id="game-session-code"></span>
                        <span id="player-count"></span>
                    </div>
                </header>

                <div class="game-content">
                    <div class="chat-container">
                        <div id="chat-messages" class="chat-messages"></div>
                        <div id="game-status" class="game-status">Ready to begin...</div>
                        <div class="chat-input-container">
                            <input type="text" id="action-input" placeholder="Describe your action..." disabled>
                            <button id="send-action-btn" class="btn btn-primary" disabled>Send</button>
                        </div>
                    </div>

                    <div class="sidebar">
                        <div class="character-info">
                            <h4>Your Character</h4>
                            <div id="current-character" class="current-character"></div>
                        </div>
                        
                        <div class="party-info">
                            <h4>Party</h4>
                            <div id="party-list" class="party-list"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loading" class="loading hidden">
            <div class="loading-content">
                <div class="spinner"></div>
                <p id="loading-text">Loading...</p>
            </div>
        </div>

        <!-- Error Modal -->
        <div id="error-modal" class="modal hidden">
            <div class="modal-content">
                <h3>Error</h3>
                <p id="error-message"></p>
                <button id="error-ok-btn" class="btn btn-primary">OK</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
