import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

// ES6 module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import our modules
import SessionManager from './src/controllers/SessionManager.js';
import GameController from './src/controllers/GameController.js';
import Logger from './src/utils/Logger.js';

// Initialize Express app
const app = express();
const server = createServer(app);
const io = new Server(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Initialize managers
const sessionManager = new SessionManager();
const gameController = new GameController(sessionManager);
const logger = new Logger();

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
    logger.info('User connected', { socketId: socket.id });

    // Create new session
    socket.on('create_session', async (data) => {
        try {
            const session = await sessionManager.createSession(data.hostName);
            socket.join(session.id);
            socket.emit('session_created', {
                sessionCode: session.code,
                sessionId: session.id,
                hostId: socket.id
            });
            logger.info('Session created', {
                sessionId: session.id,
                sessionCode: session.code,
                hostId: socket.id
            });
        } catch (error) {
            logger.error('Error creating session', { error: error.message, socketId: socket.id });
            socket.emit('error', { message: 'Failed to create session' });
        }
    });

    // Join existing session
    socket.on('join_session', async (data) => {
        try {
            const session = await sessionManager.joinSession(data.sessionCode, data.playerName, socket.id);
            socket.join(session.id);

            // Send updated session state to all players
            io.to(session.id).emit('session_updated', session.getState());

            logger.info('Player joined session', {
                sessionId: session.id,
                sessionCode: session.code,
                playerId: socket.id,
                playerName: data.playerName
            });
        } catch (error) {
            logger.error('Error joining session', { error: error.message, socketId: socket.id });
            socket.emit('error', { message: error.message });
        }
    });

    // Start game
    socket.on('start_game', async (data) => {
        try {
            const session = sessionManager.getSessionByCode(data.sessionCode);
            if (!session) {
                throw new Error('Session not found');
            }

            if (session.hostId !== socket.id) {
                throw new Error('Only host can start the game');
            }

            await gameController.startGame(session.id, data.scenario);

            logger.info('Game started', {
                sessionId: session.id,
                sessionCode: session.code,
                hostId: socket.id
            });
        } catch (error) {
            logger.error('Error starting game', { error: error.message, socketId: socket.id });
            socket.emit('error', { message: error.message });
        }
    });

    // Player action
    socket.on('player_action', async (data) => {
        try {
            await gameController.handlePlayerAction(data.sessionCode, socket.id, data.action);
        } catch (error) {
            logger.error('Error handling player action', { error: error.message, socketId: socket.id });
            socket.emit('error', { message: error.message });
        }
    });

    // Create character
    socket.on('create_character', async (data) => {
        try {
            const session = sessionManager.getSessionByCode(data.sessionCode);
            if (!session) {
                throw new Error('Session not found');
            }

            await session.createCharacter(socket.id, data.character);
            io.to(session.id).emit('session_updated', session.getState());

            logger.info('Character created', {
                sessionId: session.id,
                playerId: socket.id,
                character: data.character
            });
        } catch (error) {
            logger.error('Error creating character', { error: error.message, socketId: socket.id });
            socket.emit('error', { message: error.message });
        }
    });

    // Generate character
    socket.on('generate_character', async (data) => {
        try {
            const character = await gameController.generateCharacter(data.preferences);
            socket.emit('character_generated', { character });

            logger.info('Character generated', {
                socketId: socket.id,
                preferences: data.preferences
            });
        } catch (error) {
            logger.error('Error generating character', { error: error.message, socketId: socket.id });
            socket.emit('error', { message: error.message });
        }
    });

    // Generate scenario
    socket.on('generate_scenario', async (data) => {
        try {
            const scenario = await gameController.generateScenario(data.preferences);
            socket.emit('scenario_generated', { scenario });

            logger.info('Scenario generated', {
                socketId: socket.id,
                preferences: data.preferences
            });
        } catch (error) {
            logger.error('Error generating scenario', { error: error.message, socketId: socket.id });
            socket.emit('error', { message: error.message });
        }
    });

    // Disconnect handling
    socket.on('disconnect', () => {
        logger.info('User disconnected', { socketId: socket.id });
        sessionManager.handlePlayerDisconnect(socket.id);
    });
});

// Start server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    logger.info(`Server running on port ${PORT}`);
    console.log(`🎲 AI Dungeon Master server running on http://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    server.close(() => {
        logger.info('Server closed');
        process.exit(0);
    });
});

export default app;