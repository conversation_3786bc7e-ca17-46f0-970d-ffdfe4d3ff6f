# 🎲 Примеры использования AI Dungeon Master

## 🎮 Пример игровой сессии

### 1. Создание игры
**Хост (Алиса):**
- Заходит на http://localhost:3000
- Нажимает "Create New Game"
- Вводит имя "Алиса"
- Получает код сессии: `BRAVE-DRAGON-123`

### 2. Подключение игроков
**Игрок 1 (Боб):**
- Заходит на сайт
- Нажимает "Join Game"
- Вводит код `BRAVE-DRAGON-123` и имя "Боб"

**Игрок 2 (Клара):**
- Аналогично подключается с именем "Клара"

### 3. Настройка сценария
**Хост генерирует сценарий:**
```
Нажимает "Generate Scenario"
ИИ создает: "The Missing Merchant"
Описание: "A local merchant has gone missing, and the party is asked to investigate."
```

### 4. Создание персонажей
**Алиса создает персонажа:**
- Имя: Эльфийка Лира
- Раса: Elf
- Класс: Ranger
- Предыстория: Folk Hero

**Боб генерирует персонажа:**
```
ИИ создает:
- Имя: Торин Железнобород
- Раса: Dwarf
- Класс: Fighter
- Предыстория: Soldier
```

**Клара создает персонажа:**
- Имя: Мистик Зара
- Раса: Human
- Класс: Wizard
- Предыстория: Sage

### 5. Начало игры
**Хост нажимает "Start Adventure"**

**ИИ-Мастер описывает:**
```
Вы находитесь в таверне "Золотой Дракон" в небольшом городке Ривердейл. 
Дождливый вечер, и немногие посетители сидят у камина. К вашему столику 
подходит пожилой мужчина в дорогой одежде - это староста города.

"Добрые путешественники," - говорит он, - "нам нужна ваша помощь. 
Торговец Маркус исчез три дня назад по дороге в соседний город. 
Его повозка так и не прибыла. Не поможете ли вы найти его?"

Что вы делаете?
```

### 6. Игровые действия

**Лира (Алиса) пишет:**
```
"Я расспрашиваю старосту о подробностях - когда именно Маркус уехал, 
что он вез, кто его видел последним?"
```

**ИИ-Мастер:**
```
Классификация: DIALOGUE
Проверка: Investigation vs DC 12
Бросок: 1d20+3 = 16 (Успех!)

Староста охотно отвечает: "Маркус уехал в понедельник утром с повозкой, 
полной тканей и специй. Его видели на северной дороге около полудня. 
Обычно путь занимает день, но он так и не прибыл в Миллхейвен."
```

**Торин (Боб) пишет:**
```
"Я предлагаю осмотреть место, где его видели последним. 
Пойдем к северной дороге и поищем следы."
```

**ИИ-Мастер:**
```
Классификация: MOVEMENT + SKILL_CHECK
Вы направляетесь к северной дороге. Дождь прекратился, 
но земля еще влажная.

Торин, сделай проверку Survival для поиска следов.
Проверка: Survival vs DC 13
Бросок: 1d20+1 = 14 (Успех!)

Ты находишь следы колес повозки, ведущие на север, но примерно 
через милю они сворачивают в лес. Рядом видны следы нескольких 
пар сапог - похоже, повозку заставили свернуть с дороги.
```

**Зара (Клара) пишет:**
```
"Я использую заклинание Detect Magic, чтобы проверить, 
нет ли магических следов в этом месте."
```

**ИИ-Мастер:**
```
Классификация: SPELL_CASTING
Зара произносит заклинание, и ее глаза начинают светиться 
слабым голубым светом.

Проверка: Автоматический успех (заклинание 1 уровня)

Ты не обнаруживаешь активной магии в этом месте, но чувствуешь 
слабый остаточный след иллюзорной магии - кто-то недавно 
использовал здесь заклинание сокрытия.
```

### 7. Продолжение приключения

**Команда следует по следам в лес...**

**ИИ-Мастер:**
```
Следуя по следам, вы углубляетесь в темный лес. Через полчаса 
пути вы слышите звуки - чей-то крик о помощи! Звук доносится 
из небольшой лощины впереди.

Что вы делаете?
```

## 🎯 Примеры различных типов действий

### Боевые действия
```
Игрок: "Я атакую гоблина своим мечом"
ИИ: 
- Классификация: COMBAT
- Атака: 1d20+5 vs AC 15 = 18 (Попадание!)
- Урон: 1d8+3 = 7 колющего урона
- Описание: "Твой меч находит цель между пластинами брони гоблина..."
```

### Проверки навыков
```
Игрок: "Я пытаюсь взломать замок"
ИИ:
- Классификация: SKILL_CHECK
- Навык: Sleight of Hand vs DC 15
- Бросок: 1d20+4 = 12 (Неудача)
- Описание: "Отмычка ломается в замке со звонким щелчком..."
```

### Социальные взаимодействия
```
Игрок: "Я пытаюсь убедить стражника пропустить нас"
ИИ:
- Классификация: DIALOGUE
- Навык: Persuasion vs DC 14
- Бросок: 1d20+2 = 16 (Успех!)
- Описание: "Стражник колеблется, затем кивает: 'Ладно, проходите, но быстро!'"
```

### Исследование
```
Игрок: "Я внимательно осматриваю комнату на предмет ловушек"
ИИ:
- Классификация: SKILL_CHECK
- Навык: Investigation vs DC 13
- Бросок: 1d20+3 = 19 (Критический успех!)
- Описание: "Ты замечаешь едва видимую проволоку у входа и странные отверстия в стенах..."
```

## 🤖 Примеры работы ИИ модулей

### Классификатор действий
```javascript
Ввод: "Я крадусь к двери и прислушиваюсь"
Вывод: {
  category: "SKILL_CHECK",
  intent: "Stealth approach and listen for sounds",
  skill: "Stealth",
  difficulty: 12,
  confidence: 0.9
}
```

### Генератор персонажей
```javascript
Ввод: { preferences: { class: "Wizard" } }
Вывод: {
  name: "Элдрик Звездочет",
  race: "High Elf",
  class: "Wizard",
  background: "Sage",
  stats: { str: 8, dex: 14, con: 13, int: 16, wis: 12, cha: 10 },
  backstory: "Элдрик провел годы в изучении древних томов..."
}
```

### Генератор сценариев
```javascript
Ввод: { preferences: { theme: "mystery" } }
Вывод: {
  title: "Тайна Исчезнувшей Деревни",
  summary: "Целая деревня исчезла за одну ночь, оставив только пустые дома",
  hook: "Путешественник просит героев расследовать странное исчезновение",
  challenges: ["Поиск улик", "Встреча с выжившими", "Противостояние темной магии"]
}
```

## 📊 Примеры логов

### Лог создания сессии
```json
{"timestamp":"2025-10-07T14:00:00.000Z","level":"INFO","message":"Session created","sessionId":"abc-123","sessionCode":"BRAVE-DRAGON-456","hostId":"socket123"}
```

### Лог действия игрока
```json
{"timestamp":"2025-10-07T14:05:30.000Z","level":"INFO","message":"Player action","sessionId":"abc-123","playerId":"socket456","playerName":"Алиса","action":"Я осматриваю комнату","event":"PLAYER_ACTION"}
```

### Лог запроса к ИИ
```json
{"timestamp":"2025-10-07T14:05:31.000Z","level":"INFO","message":"LLM request","sessionId":"abc-123","module":"Classifier","model":"gpt-4o-mini","promptLength":150,"event":"LLM_REQUEST"}
```

### Лог ответа ИИ
```json
{"timestamp":"2025-10-07T14:05:33.000Z","level":"INFO","message":"LLM response","sessionId":"abc-123","module":"Classifier","model":"gpt-4o-mini","responseLength":200,"duration":2000,"event":"LLM_RESPONSE"}
```

## 🎨 Примеры кастомизации

### Создание собственного сценария
```
Заголовок: "Проклятие Старого Маяка"
Описание: "Рыбаки сообщают о странных огнях в заброшенном маяке. 
Каждую ночь оттуда доносятся жуткие звуки, а корабли начали 
исчезать в тумане. Местные жители боятся приближаться к берегу."
```

### Создание уникального персонажа
```
Имя: Шепот Теней
Раса: Tiefling
Класс: Warlock
Предыстория: "Заключил пакт с таинственной сущностью из Теневого Плана 
в обмен на силу отомстить за свою семью."
```

## 🔧 Технические примеры

### WebSocket события
```javascript
// Создание сессии
socket.emit('create_session', { hostName: 'Алиса' });

// Подключение к сессии
socket.emit('join_session', { 
  sessionCode: 'BRAVE-DRAGON-123', 
  playerName: 'Боб' 
});

// Действие игрока
socket.emit('player_action', {
  sessionCode: 'BRAVE-DRAGON-123',
  action: 'Я атакую гоблина'
});
```

### Структура состояния сессии
```javascript
{
  id: "session-uuid",
  code: "BRAVE-DRAGON-123",
  players: [
    { id: "socket1", name: "Алиса", isHost: true, isReady: true },
    { id: "socket2", name: "Боб", isHost: false, isReady: true }
  ],
  characters: [
    { name: "Лира", race: "Elf", class: "Ranger", playerId: "socket1" },
    { name: "Торин", race: "Dwarf", class: "Fighter", playerId: "socket2" }
  ],
  gameState: "PLAYING",
  currentStatus: "AWAITING_ACTIONS",
  chatHistory: [...]
}
```

---

Эти примеры показывают, как AI Dungeon Master создает увлекательный и интерактивный игровой опыт, автоматически управляя всеми аспектами D&D сессии!
