import BaseAIModule from './BaseAIModule.js';

/**
 * AI module for roleplaying NPCs and handling dialogue
 */
export default class NPCModule extends BaseAIModule {
  constructor() {
    super('creative'); // Use creative model for NPC roleplay
  }

  /**
   * Generate NPC response to player dialogue
   */
  async generateNPCResponse(npcData, playerMessage, gameContext) {
    const systemPrompt = `You are roleplaying as an NPC in a D&D adventure. Stay in character and respond naturally to player dialogue.

NPC Information:
Name: ${npcData.name}
Description: ${npcData.description || 'A typical NPC'}
Personality: ${npcData.personality || 'Neutral and helpful'}
Knowledge: ${npcData.knowledge || 'Basic local information'}
Goals: ${npcData.goals || 'None specified'}
Relationship to party: ${npcData.relationship || 'Neutral'}

ROLEPLAY GUIDELINES:
- Stay in character at all times
- Speak in the NPC's voice and manner
- Provide information the NPC would realistically know
- React appropriately to the player's tone and approach
- Include personality quirks and speech patterns
- Don't reveal information the NPC wouldn't know
- Keep responses conversational and natural

Game Context: ${this.truncateText(gameContext)}`;

    const userPrompt = `Player says: "${playerMessage}"

Respond as ${npcData.name} would respond. Include both dialogue and any actions/reactions.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 400,
        temperature: 0.8
      });

      return {
        npcName: npcData.name,
        response: response.content.trim(),
        type: 'dialogue',
        processingTime: response.duration
      };
    } catch (error) {
      console.error('NPC response generation error:', error);
      return {
        npcName: npcData.name,
        response: `${npcData.name} looks at you thoughtfully but doesn't respond immediately.`,
        type: 'dialogue',
        error: error.message
      };
    }
  }

  /**
   * Generate a new NPC on the fly
   */
  async generateNPC(context, role = 'generic', location = '') {
    const systemPrompt = `Create a new NPC for a D&D adventure. Provide a complete character that fits the context and role.

Respond with JSON containing:
- "name": NPC's name
- "description": Physical appearance and notable features
- "personality": Key personality traits and mannerisms
- "background": Brief background and history
- "knowledge": What they know about local area/events
- "goals": What they want or need
- "secrets": Any hidden information (if applicable)
- "relationship": How they initially view the party
- "speechPattern": How they speak (formal, casual, accent, etc.)

Role: ${role}
Location: ${location}
Context: ${this.truncateText(context)}`;

    const userPrompt = `Generate an NPC that fits this role and context. Make them interesting and memorable.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 600,
        temperature: 0.9
      });

      const npcData = this.parseJSONResponse(response.content);
      this.validateResponse(npcData, ['name', 'description', 'personality']);

      return {
        ...npcData,
        id: `npc_${Date.now()}`,
        createdAt: new Date(),
        type: 'generated_npc',
        processingTime: response.duration
      };
    } catch (error) {
      console.error('NPC generation error:', error);
      
      // Return basic fallback NPC
      return {
        id: `npc_${Date.now()}`,
        name: 'Local Resident',
        description: 'An ordinary person going about their daily business',
        personality: 'Cautious but helpful',
        background: 'A local resident with basic knowledge of the area',
        knowledge: 'Basic information about the local area',
        goals: 'To live peacefully',
        secrets: 'None',
        relationship: 'Neutral',
        speechPattern: 'Simple and direct',
        type: 'generated_npc',
        error: error.message
      };
    }
  }

  /**
   * Handle NPC reactions to player actions
   */
  async generateNPCReaction(npcData, playerAction, actionResult, gameContext) {
    const systemPrompt = `You are determining how an NPC reacts to a player's action in a D&D game.

NPC: ${npcData.name}
Personality: ${npcData.personality}
Relationship: ${npcData.relationship}

Consider:
- The NPC's personality and goals
- Their relationship with the party
- The nature and outcome of the action
- Realistic emotional responses
- How this might affect future interactions

Provide both immediate reaction and any lasting attitude changes.

Game Context: ${this.truncateText(gameContext)}`;

    const userPrompt = `Player Action: ${playerAction}
Action Result: ${actionResult.success ? 'Success' : 'Failure'}
${actionResult.description || ''}

How does ${npcData.name} react to this?`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 300,
        temperature: 0.7
      });

      return {
        npcName: npcData.name,
        reaction: response.content.trim(),
        type: 'reaction',
        processingTime: response.duration
      };
    } catch (error) {
      console.error('NPC reaction generation error:', error);
      return {
        npcName: npcData.name,
        reaction: `${npcData.name} watches the events unfold with interest.`,
        type: 'reaction',
        error: error.message
      };
    }
  }

  /**
   * Generate NPC initiative in conversation
   */
  async generateNPCInitiative(npcData, situation, gameContext) {
    const systemPrompt = `The NPC is taking initiative in a conversation or situation. Generate what they say or do.

NPC: ${npcData.name}
Personality: ${npcData.personality}
Goals: ${npcData.goals}

The NPC should act according to their personality and goals in this situation.

Game Context: ${this.truncateText(gameContext)}`;

    const userPrompt = `Situation: ${situation}

What does ${npcData.name} say or do in this situation?`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 300,
        temperature: 0.8
      });

      return {
        npcName: npcData.name,
        initiative: response.content.trim(),
        type: 'initiative',
        processingTime: response.duration
      };
    } catch (error) {
      console.error('NPC initiative generation error:', error);
      return {
        npcName: npcData.name,
        initiative: `${npcData.name} seems to be thinking about something.`,
        type: 'initiative',
        error: error.message
      };
    }
  }

  /**
   * Update NPC relationship based on interactions
   */
  updateNPCRelationship(npcData, interactionType, outcome) {
    const relationshipMap = {
      'hostile': -2,
      'unfriendly': -1,
      'neutral': 0,
      'friendly': 1,
      'helpful': 2
    };

    let currentValue = relationshipMap[npcData.relationship] || 0;
    let change = 0;

    // Determine relationship change based on interaction
    switch (interactionType) {
      case 'successful_persuasion':
        change = 1;
        break;
      case 'failed_deception':
        change = -1;
        break;
      case 'intimidation':
        change = outcome === 'success' ? -1 : 0;
        break;
      case 'helpful_action':
        change = 1;
        break;
      case 'harmful_action':
        change = -2;
        break;
      case 'gift_given':
        change = 1;
        break;
      default:
        change = 0;
    }

    const newValue = Math.max(-2, Math.min(2, currentValue + change));
    const relationships = ['hostile', 'unfriendly', 'neutral', 'friendly', 'helpful'];
    const newRelationship = relationships[newValue + 2];

    return {
      oldRelationship: npcData.relationship,
      newRelationship,
      change,
      reason: interactionType
    };
  }
}
