# 🚀 Развертывание AI Dungeon Master

## 📋 Требования к системе

### Минимальные требования
- **Node.js**: версия 18.0 или выше
- **NPM**: версия 8.0 или выше
- **RAM**: минимум 512 MB
- **Дисковое пространство**: 100 MB для приложения + место для логов
- **Интернет**: для работы с OpenAI API

### Рекомендуемые требования
- **Node.js**: версия 20.0 или выше
- **RAM**: 1 GB или больше
- **CPU**: 2 ядра или больше
- **SSD**: для быстрого доступа к логам

## 🔧 Локальная установка

### 1. Клонирование репозитория
```bash
git clone <repository-url>
cd neurodnd
```

### 2. Установка зависимостей
```bash
npm install
```

### 3. Настройка окружения
Создайте файл `.env` на основе примера:
```bash
cp .env.example .env
```

Отредактируйте `.env` файл:
```env
# OpenAI API Configuration
OPENAI_API_KEY=sk-your-actual-openai-api-key-here

# Server Configuration
PORT=3000
NODE_ENV=production

# AI Model Configuration
AI_MODEL_CREATIVE=gpt-4o
AI_MODEL_STRUCTURED=gpt-4o-mini

# Session Configuration
SESSION_TIMEOUT_MINUTES=120
MAX_PLAYERS_PER_SESSION=6

# Logging Configuration
LOG_LEVEL=info
LOG_DIR=./logs
```

### 4. Тестирование
```bash
node test.js
```

### 5. Запуск
```bash
npm start
```

Приложение будет доступно по адресу: http://localhost:3000

## 🌐 Развертывание на сервере

### Подготовка сервера (Ubuntu/Debian)

#### 1. Обновление системы
```bash
sudo apt update && sudo apt upgrade -y
```

#### 2. Установка Node.js
```bash
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### 3. Установка PM2 (менеджер процессов)
```bash
sudo npm install -g pm2
```

#### 4. Создание пользователя для приложения
```bash
sudo adduser neurodnd
sudo usermod -aG sudo neurodnd
```

### Развертывание приложения

#### 1. Загрузка кода
```bash
su - neurodnd
git clone <repository-url>
cd neurodnd
npm install --production
```

#### 2. Настройка окружения
```bash
cp .env.example .env
nano .env
```

#### 3. Создание PM2 конфигурации
Создайте файл `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'neurodnd',
    script: 'app.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true
  }]
};
```

#### 4. Запуск с PM2
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Настройка Nginx (опционально)

#### 1. Установка Nginx
```bash
sudo apt install nginx -y
```

#### 2. Создание конфигурации
Создайте файл `/etc/nginx/sites-available/neurodnd`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket support
    location /socket.io/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 3. Активация конфигурации
```bash
sudo ln -s /etc/nginx/sites-available/neurodnd /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🔒 SSL/HTTPS (Let's Encrypt)

### 1. Установка Certbot
```bash
sudo apt install certbot python3-certbot-nginx -y
```

### 2. Получение сертификата
```bash
sudo certbot --nginx -d your-domain.com
```

### 3. Автоматическое обновление
```bash
sudo crontab -e
# Добавьте строку:
0 12 * * * /usr/bin/certbot renew --quiet
```

## 🐳 Docker развертывание

### 1. Создание Dockerfile
```dockerfile
FROM node:20-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

RUN mkdir -p logs

EXPOSE 3000

USER node

CMD ["npm", "start"]
```

### 2. Создание docker-compose.yml
```yaml
version: '3.8'

services:
  neurodnd:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### 3. Запуск
```bash
docker-compose up -d
```

## 📊 Мониторинг и логи

### Просмотр логов PM2
```bash
pm2 logs neurodnd
pm2 monit
```

### Просмотр логов приложения
```bash
tail -f logs/neurodnd-$(date +%Y-%m-%d).jsonl
```

### Мониторинг ресурсов
```bash
# CPU и память
htop

# Дисковое пространство
df -h

# Сетевые соединения
netstat -tulpn | grep :3000
```

## 🔧 Обслуживание

### Обновление приложения
```bash
git pull origin main
npm install --production
pm2 restart neurodnd
```

### Очистка старых логов
```bash
# Удаление логов старше 30 дней
find logs/ -name "*.jsonl" -mtime +30 -delete
```

### Резервное копирование
```bash
# Создание архива
tar -czf neurodnd-backup-$(date +%Y%m%d).tar.gz \
  --exclude=node_modules \
  --exclude=logs \
  .
```

## 🚨 Устранение неполадок

### Проблемы с запуском
```bash
# Проверка статуса
pm2 status

# Перезапуск
pm2 restart neurodnd

# Просмотр ошибок
pm2 logs neurodnd --err
```

### Проблемы с WebSocket
```bash
# Проверка портов
sudo netstat -tulpn | grep :3000

# Проверка firewall
sudo ufw status
sudo ufw allow 3000
```

### Проблемы с OpenAI API
```bash
# Проверка API ключа
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/models
```

## 📈 Масштабирование

### Горизонтальное масштабирование
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'neurodnd',
    script: 'app.js',
    instances: 'max', // Использовать все CPU ядра
    exec_mode: 'cluster'
  }]
};
```

### Балансировка нагрузки (Nginx)
```nginx
upstream neurodnd_backend {
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
}

server {
    location / {
        proxy_pass http://neurodnd_backend;
    }
}
```

## 🔐 Безопасность

### Настройка firewall
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw deny 3000  # Закрыть прямой доступ к приложению
```

### Ограничение доступа к логам
```bash
chmod 750 logs/
chown neurodnd:neurodnd logs/
```

### Регулярные обновления
```bash
# Автоматические обновления безопасности
sudo apt install unattended-upgrades -y
sudo dpkg-reconfigure unattended-upgrades
```

---

**Готово! Ваш AI Dungeon Master развернут и готов к использованию! 🎲**
