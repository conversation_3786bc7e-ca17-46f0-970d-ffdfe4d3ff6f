# 🎲 AI Dungeon Master

Прототип веб-приложения для проведения настольных ролевых игр (D&D) под управлением ИИ-Мастера. Система обеспечивает совместную игру нескольких пользователей в реальном времени через текстовый чат.

## ✨ Особенности

- **ИИ-Мастер**: Полностью автоматизированное ведение игры с помощью LLM
- **Многопользовательская игра**: До 6 игроков в одной сессии
- **Реальное время**: WebSocket соединение для мгновенных обновлений
- **Генерация контента**: Автоматическое создание персонажей, сценариев и событий
- **Система правил D&D 5e**: Автоматические проверки навыков и механики
- **Детальное логирование**: Все события записываются в формате JSONL

## 🏗️ Архитектура

### Бэкенд (Node.js + Express + Socket.IO)
- **SessionManager**: Управление игровыми сессиями
- **GameController**: Координация игрового процесса
- **AI Модули**: Специализированные компоненты для разных задач
  - **ClassifierModule**: Классификация действий игроков
  - **RulesModule**: Интерпретация правил D&D
  - **NarrativeModule**: Генерация описаний и нарратива
  - **NPCModule**: Отыгрыш неигровых персонажей
  - **GeneratorModule**: Создание персонажей и сценариев

### Фронтенд (Vanilla JS + WebSocket)
- **SPA интерфейс**: Главная страница, лобби, игровой чат
- **Реальное время**: Мгновенные обновления через WebSocket
- **Адаптивный дизайн**: Работает на десктопах и мобильных устройствах

## 🚀 Быстрый старт

### 1. Установка зависимостей
```bash
npm install
```

### 2. Настройка окружения
Скопируйте `.env` файл и добавьте ваш OpenAI API ключ:
```bash
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Server Configuration
PORT=3000
NODE_ENV=development

# AI Model Configuration
AI_MODEL_CREATIVE=gpt-4o
AI_MODEL_STRUCTURED=gpt-4o-mini
```

### 3. Запуск сервера
```bash
npm start
```

### 4. Открытие в браузере
Перейдите на http://localhost:3000

## 🎮 Как играть

### Создание игры
1. Нажмите "Create New Game"
2. Введите ваше имя
3. Поделитесь кодом сессии с друзьями

### Подключение к игре
1. Нажмите "Join Game"
2. Введите код сессии и ваше имя
3. Попадете в лобби игры

### Подготовка к игре
1. **Создайте сценарий**: Используйте генератор ИИ или введите свой
2. **Создайте персонажа**: Заполните форму или сгенерируйте автоматически
3. **Начните игру**: Хост нажимает "Start Adventure"

### Игровой процесс
1. Читайте описания от ИИ-Мастера
2. Вводите ваши действия в свободной форме
3. ИИ обрабатывает действия и описывает результаты
4. Система автоматически выполняет проверки навыков

## 🔧 Технические детали

### Используемые технологии
- **Backend**: Node.js, Express, Socket.IO
- **AI**: OpenAI GPT-4o и GPT-4o-mini
- **Frontend**: Vanilla JavaScript, WebSocket
- **Логирование**: JSONL формат

### Структура проекта
```
neurodnd/
├── src/
│   ├── ai/              # AI модули
│   ├── controllers/     # Контроллеры игры
│   ├── models/          # Модели данных
│   └── utils/           # Утилиты
├── public/              # Фронтенд файлы
├── logs/                # Лог файлы
├── app.js               # Главный файл сервера
└── test.js              # Тесты
```

### API Endpoints
- `GET /` - Главная страница
- `GET /health` - Проверка состояния сервера
- WebSocket события:
  - `create_session` - Создание сессии
  - `join_session` - Подключение к сессии
  - `start_game` - Начало игры
  - `player_action` - Действие игрока
  - `create_character` - Создание персонажа
  - `generate_character` - Генерация персонажа
  - `generate_scenario` - Генерация сценария

## 📊 Логирование

Все события записываются в `logs/neurodnd-YYYY-MM-DD.jsonl`:
```json
{"timestamp":"2025-10-07T13:42:57.850Z","level":"INFO","message":"Server running on port 3000"}
{"timestamp":"2025-10-07T13:43:22.351Z","level":"INFO","message":"User connected","socketId":"nUeMpnD_jWbx3cLoAAAB"}
{"timestamp":"2025-10-07T13:43:30.123Z","level":"INFO","message":"Session created","sessionId":"abc-123","sessionCode":"BRAVE-DRAGON-456"}
```

## 🧪 Тестирование

Запустите тесты:
```bash
node test.js
```

Тесты проверяют:
- Создание и управление сессиями
- Подключение игроков
- Создание персонажей
- Генерацию кодов сессий
- Систему логирования
- Fallback функции AI модулей

## 🔒 Безопасность

- Доступ к сессиям только по уникальным кодам
- Нет регистрации или хранения персональных данных
- Автоматическое удаление неактивных сессий
- Валидация всех пользовательских вводов

## 🎯 Ограничения MVP

- Максимум 6 игроков в сессии
- Только текстовый интерфейс
- Базовая система правил D&D 5e
- Нет сохранения игр между сессиями
- Требуется OpenAI API ключ

## 🚧 Известные проблемы

- При отсутствии API ключа AI функции используют fallback методы
- Сессии удаляются при перезапуске сервера
- Нет системы восстановления соединения

## 📈 Планы развития

- [ ] Сохранение игр в базе данных
- [ ] Голосовой интерфейс
- [ ] Визуальные карты и изображения
- [ ] Расширенная система правил
- [ ] Мобильное приложение
- [ ] Интеграция с Discord

## 🤝 Вклад в проект

1. Fork репозитория
2. Создайте feature branch
3. Внесите изменения
4. Добавьте тесты
5. Создайте Pull Request

## 📄 Лицензия

ISC License

## 🆘 Поддержка

При возникновении проблем:
1. Проверьте логи в папке `logs/`
2. Убедитесь, что OpenAI API ключ корректен
3. Проверьте подключение к интернету
4. Перезапустите сервер

---

**Создано с ❤️ для любителей D&D и ИИ технологий**
