import BaseAIModule from './BaseAIModule.js';

/**
 * AI module for classifying player actions and determining intent
 */
export default class ClassifierModule extends BaseAIModule {
  constructor() {
    super('structured'); // Use structured model for classification
  }

  /**
   * Classify a player action and determine its type and intent
   */
  async classifyAction(playerAction, gameContext) {
    const systemPrompt = `You are an AI classifier for a D&D game. Your job is to analyze player actions and classify them into specific categories.

CLASSIFICATION CATEGORIES:
1. COMBAT - Any action involving fighting, attacking, or combat maneuvers
2. SKILL_CHECK - Actions requiring skill rolls (investigation, perception, athletics, etc.)
3. DIALOGUE - Speaking to NPCs or other characters
4. MOVEMENT - Moving to different locations or areas
5. INTERACTION - Interacting with objects, items, or environment
6. SPELL_CASTING - Using magic or spells
7. INVENTORY - Managing items, equipment, or inventory
8. QUESTION - Asking the DM questions about the world or situation
9. INVALID - Actions that don't make sense or are impossible

For each action, respond with a JSON object containing:
- "category": one of the categories above
- "intent": a brief description of what the player wants to do
- "target": what/who the action is directed at (if applicable)
- "skill": suggested skill for skill checks (if applicable)
- "difficulty": suggested difficulty class for skill checks (if applicable)
- "confidence": how confident you are in this classification (0.0-1.0)

Current game context: ${this.truncateText(gameContext)}`;

    const userPrompt = `Player action: "${playerAction}"

Classify this action and provide the JSON response.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 300,
        temperature: 0.1 // Very low temperature for consistent classification
      });

      const classification = this.parseJSONResponse(response.content);
      
      // Validate required fields
      this.validateResponse(classification, ['category', 'intent', 'confidence']);

      // Ensure confidence is within valid range
      classification.confidence = Math.max(0, Math.min(1, classification.confidence));

      return {
        ...classification,
        originalAction: playerAction,
        processingTime: response.duration
      };
    } catch (error) {
      console.error('Classification error:', error);
      
      // Return fallback classification
      return {
        category: 'QUESTION',
        intent: 'Player action unclear, treating as question to DM',
        target: null,
        skill: null,
        difficulty: null,
        confidence: 0.1,
        originalAction: playerAction,
        error: error.message
      };
    }
  }

  /**
   * Determine if multiple actions can be processed simultaneously
   */
  async analyzeActionSequence(actions, gameContext) {
    const systemPrompt = `You are analyzing a sequence of player actions in a D&D game to determine processing order.

Analyze the given actions and determine:
1. Which actions can be processed simultaneously
2. Which actions must be processed in sequence
3. If any actions conflict with each other
4. The optimal processing order

Respond with a JSON object containing:
- "canProcessSimultaneously": boolean
- "processingOrder": array of action indices in processing order
- "conflicts": array of conflicting action pairs
- "reasoning": explanation of your decision

Current game context: ${this.truncateText(gameContext)}`;

    const actionsText = actions.map((action, index) => 
      `${index}: ${action.playerName} - ${action.action}`
    ).join('\n');

    const userPrompt = `Actions to analyze:
${actionsText}

Provide the JSON analysis.`;

    const messages = [
      this.createSystemMessage(systemPrompt),
      this.createUserMessage(userPrompt)
    ];

    try {
      const response = await this.makeRequest(messages, {
        maxTokens: 400,
        temperature: 0.2
      });

      const analysis = this.parseJSONResponse(response.content);
      this.validateResponse(analysis, ['canProcessSimultaneously', 'processingOrder']);

      return analysis;
    } catch (error) {
      console.error('Action sequence analysis error:', error);
      
      // Return safe fallback - process actions one by one
      return {
        canProcessSimultaneously: false,
        processingOrder: actions.map((_, index) => index),
        conflicts: [],
        reasoning: 'Error in analysis, processing sequentially for safety',
        error: error.message
      };
    }
  }

  /**
   * Quick classification for common actions (cached responses)
   */
  getQuickClassification(action) {
    const actionLower = action.toLowerCase();
    
    // Combat keywords
    if (/\b(attack|hit|strike|fight|shoot|stab|slash|punch)\b/.test(actionLower)) {
      return {
        category: 'COMBAT',
        intent: 'Engage in combat',
        confidence: 0.9,
        isQuickClassification: true
      };
    }
    
    // Movement keywords
    if (/\b(go|move|walk|run|travel|head|approach)\b/.test(actionLower)) {
      return {
        category: 'MOVEMENT',
        intent: 'Move to a location',
        confidence: 0.8,
        isQuickClassification: true
      };
    }
    
    // Investigation keywords
    if (/\b(look|search|examine|investigate|check|inspect)\b/.test(actionLower)) {
      return {
        category: 'SKILL_CHECK',
        intent: 'Investigate or examine something',
        skill: 'Investigation',
        difficulty: 12,
        confidence: 0.8,
        isQuickClassification: true
      };
    }
    
    // Dialogue keywords
    if (/\b(say|tell|ask|speak|talk|greet|hello)\b/.test(actionLower)) {
      return {
        category: 'DIALOGUE',
        intent: 'Communicate with someone',
        confidence: 0.8,
        isQuickClassification: true
      };
    }
    
    return null; // No quick classification available
  }
}
