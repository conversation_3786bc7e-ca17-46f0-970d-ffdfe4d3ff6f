/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #333;
    min-height: 100vh;
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.screen {
    display: none;
    min-height: 100vh;
}

.screen.active {
    display: block;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 3rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Cards and sections */
.option-card,
.scenario-display,
.character-display {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: transform 0.2s ease;
}

.option-card:hover {
    transform: translateY(-2px);
}

.menu-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

/* Buttons */
.btn {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.2s ease;
    margin: 5px;
}

.btn:hover {
    background: #45a049;
    transform: translateY(-1px);
}

.btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: #2196F3;
}

.btn-primary:hover {
    background: #1976D2;
}

.btn-secondary {
    background: #FF9800;
}

.btn-secondary:hover {
    background: #F57C00;
}

.btn-small {
    padding: 6px 12px;
    font-size: 14px;
}

/* Form elements */
input,
select,
textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    margin: 8px 0;
    transition: border-color 0.2s ease;
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: #2196F3;
}

textarea {
    min-height: 100px;
    resize: vertical;
}

/* Lobby styles */
.lobby-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.session-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.lobby-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.players-section,
.scenario-section,
.character-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.character-section {
    grid-column: span 2;
}

.players-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.player-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 6px;
}

.player-host {
    background: #e3f2fd;
}

/* Game screen */
.game-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.game-header {
    background: white;
    padding: 15px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.game-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 300px;
    height: calc(100vh - 70px);
}

.chat-container {
    display: flex;
    flex-direction: column;
    background: white;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    padding: 12px;
    border-radius: 8px;
    max-width: 80%;
}

.message-narrator {
    background: #e8f4fd;
    border-left: 4px solid #2196F3;
    margin-left: 0;
}

.message-player {
    background: #f0f8e8;
    border-left: 4px solid #4CAF50;
    margin-left: auto;
    margin-right: 0;
}

.message-npc {
    background: #fff3e0;
    border-left: 4px solid #FF9800;
}

.message-system {
    background: #f3e5f5;
    border-left: 4px solid #9C27B0;
    font-style: italic;
}

.message-header {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 14px;
}

.game-status {
    padding: 15px 20px;
    background: #e3f2fd;
    border-top: 1px solid #ddd;
    font-weight: 500;
    text-align: center;
}

.chat-input-container {
    display: flex;
    padding: 15px 20px;
    background: white;
    border-top: 1px solid #ddd;
}

.chat-input-container input {
    flex: 1;
    margin: 0;
    margin-right: 10px;
}

.sidebar {
    background: white;
    border-left: 1px solid #ddd;
    padding: 20px;
    overflow-y: auto;
}

.character-info,
.party-info {
    margin-bottom: 30px;
}

.character-info h4,
.party-info h4 {
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #eee;
    padding-bottom: 5px;
}

/* Utility classes */
.hidden {
    display: none !important;
}

.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    background: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 12px;
    max-width: 400px;
    width: 90%;
    text-align: center;
}

.modal-content h3 {
    margin-bottom: 15px;
    color: #d32f2f;
}

/* Skill check display */
.skill-check {
    background: #f0f4f8;
    border: 1px solid #cbd5e0;
    border-radius: 6px;
    padding: 10px;
    margin: 5px 0;
    font-family: monospace;
}

.skill-check.success {
    border-color: #4CAF50;
    background: #e8f5e9;
}

.skill-check.failure {
    border-color: #f44336;
    background: #ffebee;
}

/* Responsive design */
@media (max-width: 768px) {
    .lobby-content {
        grid-template-columns: 1fr;
    }

    .character-section {
        grid-column: span 1;
    }

    .game-content {
        grid-template-columns: 1fr;
    }

    .sidebar {
        display: none;
    }

    .menu-options {
        grid-template-columns: 1fr;
    }
}