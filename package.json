{"name": "neurodnd", "version": "1.0.0", "description": "AI Dungeon Master - LLM-driven D&D sessions", "main": "app.js", "type": "module", "scripts": {"start": "node app.js", "dev": "node app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["dnd", "ai", "llm", "rpg", "dungeon-master"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.3", "express": "^5.1.0", "fs-extra": "^11.3.2", "openai": "^6.2.0", "socket.io": "^4.8.1", "uuid": "^13.0.0"}}